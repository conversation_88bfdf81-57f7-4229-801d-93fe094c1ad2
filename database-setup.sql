-- 数据库初始化脚本
-- 用于创建本地开发环境的数据库

-- 创建数据库
CREATE DATABASE IF NOT EXISTS nettyserver 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE nettyserver;

-- 创建数据库用户（如果不存在）
-- 注意：在生产环境中请使用更安全的密码
CREATE USER IF NOT EXISTS 'dbadmin'@'localhost' IDENTIFIED BY 'dbadmin';
CREATE USER IF NOT EXISTS 'dbadmin'@'%' IDENTIFIED BY 'dbadmin';

-- 授予权限
GRANT ALL PRIVILEGES ON nettyserver.* TO 'dbadmin'@'localhost';
GRANT ALL PRIVILEGES ON nettyserver.* TO 'dbadmin'@'%';

-- 刷新权限
FLUSH PRIVILEGES;

-- 创建基本的测试表（如果项目需要）
CREATE TABLE IF NOT EXISTS test_connection (
    id INT AUTO_INCREMENT PRIMARY KEY,
    test_message VARCHAR(255) DEFAULT 'Database connection successful',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 插入测试数据
INSERT INTO test_connection (test_message) VALUES ('Initial test record');

-- 显示创建结果
SELECT 'Database setup completed successfully!' as status;
SELECT * FROM test_connection;
