<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="s" uri="/struts-tags"%>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>编码测试页面</title>
    <script src="/808FrontProject/js/encoding-utils.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
    </style>
</head>
<body>
    <h1>编码测试页面</h1>
    
    <div class="test-section">
        <h2>中文显示测试</h2>
        <p class="info">如果您能正确看到以下中文内容，说明编码配置正确：</p>
        <ul>
            <li>车辆监控系统</li>
            <li>用户管理</li>
            <li>驾驶员信息</li>
            <li>位置查询</li>
            <li>报表统计</li>
        </ul>
    </div>
    
    <div class="test-section">
        <h2>特殊字符测试</h2>
        <p>特殊字符：©®™€£¥§¶†‡•…‰‹›""''</p>
        <p>数学符号：±×÷≤≥≠∞∑∏∫√∂∆∇</p>
        <p>中文标点：，。；：？！""''（）【】《》</p>
    </div>
    
    <div class="test-section">
        <h2>AJAX编码测试</h2>
        <button onclick="testAjaxEncoding()">测试AJAX请求编码</button>
        <div id="ajax-result"></div>
    </div>
    
    <div class="test-section">
        <h2>表单提交测试</h2>
        <form method="post" action="#" onsubmit="return testFormSubmit(event)">
            <label>中文输入测试：</label>
            <input type="text" name="chineseText" value="测试中文输入" style="width: 200px;">
            <button type="submit">提交测试</button>
        </form>
        <div id="form-result"></div>
    </div>
    
    <div class="test-section">
        <h2>编码信息</h2>
        <p>页面编码：<span id="page-encoding"></span></p>
        <p>浏览器编码：<span id="browser-encoding"></span></p>
        <p>文档编码：<span id="document-encoding"></span></p>
    </div>

    <script>
        // 显示编码信息
        document.getElementById('page-encoding').textContent = document.characterSet || document.charset || '未知';
        document.getElementById('browser-encoding').textContent = navigator.language || '未知';
        document.getElementById('document-encoding').textContent = document.inputEncoding || '未知';
        
        // AJAX编码测试
        function testAjaxEncoding() {
            var resultDiv = document.getElementById('ajax-result');
            resultDiv.innerHTML = '<p class="info">正在测试AJAX编码...</p>';
            
            // 使用原生XMLHttpRequest测试
            var xhr = new XMLHttpRequest();
            xhr.open('POST', '/808FrontProject/common/validateUserNameAction.action', true);
            xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded; charset=UTF-8');
            xhr.setRequestHeader('Accept-Charset', 'UTF-8');
            
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    if (xhr.status === 200) {
                        resultDiv.innerHTML = '<p class="success">AJAX请求成功，响应编码正常</p>';
                    } else {
                        resultDiv.innerHTML = '<p class="error">AJAX请求失败，状态码：' + xhr.status + '</p>';
                    }
                }
            };
            
            xhr.send('userName=' + encodeURIComponent('测试用户'));
        }
        
        // 表单提交测试
        function testFormSubmit(event) {
            event.preventDefault();
            var formData = new FormData(event.target);
            var chineseText = formData.get('chineseText');
            
            document.getElementById('form-result').innerHTML = 
                '<p class="success">表单数据：' + chineseText + '</p>' +
                '<p class="info">编码后：' + encodeURIComponent(chineseText) + '</p>';
            
            return false;
        }
    </script>
</body>
</html>
