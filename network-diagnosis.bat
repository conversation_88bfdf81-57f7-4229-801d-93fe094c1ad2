@echo off
echo ========================================
echo 数据库连接网络诊断工具
echo ========================================
echo.

echo 1. 测试本地MySQL连接...
echo.
netstat -an | findstr :3306
if %errorlevel% equ 0 (
    echo ✅ 本地MySQL服务正在运行 (端口3306已监听)
) else (
    echo ❌ 本地MySQL服务未运行或未监听3306端口
    echo 请启动MySQL服务或检查配置
)
echo.

echo 2. 测试远程数据库连接...
echo.
echo 正在ping远程数据库服务器...
ping -n 4 *************
if %errorlevel% equ 0 (
    echo ✅ 远程服务器可达
    echo.
    echo 正在测试远程MySQL端口...
    telnet 39.99.************
) else (
    echo ❌ 远程服务器不可达
    echo 可能原因：
    echo - 网络连接问题
    echo - 服务器防火墙阻止
    echo - 服务器未启动
)
echo.

echo 3. 检查本地网络配置...
echo.
ipconfig | findstr "IPv4"
echo.

echo 4. 检查DNS解析...
echo.
nslookup *************
echo.

echo 5. 建议的解决步骤：
echo.
echo 如果远程数据库连接失败：
echo 1. 切换到本地数据库配置
echo 2. 安装并启动本地MySQL服务
echo 3. 运行database-setup.sql创建数据库
echo 4. 使用DatabaseConnectionTest.java测试连接
echo.

echo 如果本地数据库也无法连接：
echo 1. 下载并安装MySQL Server
echo 2. 启动MySQL服务
echo 3. 使用MySQL Workbench或命令行执行database-setup.sql
echo 4. 确认用户dbadmin已创建并有适当权限
echo.

pause
