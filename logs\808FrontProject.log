2025-07-03 15:33:52.163|[localhost-startStop-1]|INFO|ContextLoader|Root WebApplicationContext: initialization started
2025-07-03 15:33:52.222|[localhost-startStop-1]|INFO|XmlWebApplicationContext|Refreshing Root WebApplicationContext: startup date [Thu Jul 03 15:33:52 CST 2025]; root of context hierarchy
2025-07-03 15:33:52.250|[localhost-startStop-1]|INFO|XmlBeanDefinitionReader|Loading XML bean definitions from file [C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\target\classes\spring-beans.xml]
2025-07-03 15:33:52.434|[localhost-startStop-1]|INFO|XmlBeanDefinitionReader|Loading XML bean definitions from file [C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\target\classes\spring-quartz.xml]
2025-07-03 15:33:52.633|[localhost-startStop-1]|INFO|PropertyPlaceholderConfigurer|Loading properties file from class path resource [datasource.properties]
2025-07-03 15:33:52.633|[localhost-startStop-1]|INFO|PropertyPlaceholderConfigurer|Loading properties file from class path resource [redis.properties]
2025-07-03 15:33:52.633|[localhost-startStop-1]|INFO|PropertyPlaceholderConfigurer|Loading properties file from class path resource [hibernate.properties]
2025-07-03 15:33:52.639|[localhost-startStop-1]|INFO|AutowiredAnnotationBeanPostProcessor|JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2025-07-03 15:33:52.924|[localhost-startStop-1]|INFO|ContextLoader|Root WebApplicationContext: initialization completed in 759 ms
2025-07-03 15:33:53.103|[localhost-startStop-1]|INFO|XmlConfigurationProvider|Parsing configuration file [struts-default.xml]
2025-07-03 15:33:53.150|[localhost-startStop-1]|INFO|XmlConfigurationProvider|Parsing configuration file [struts-plugin.xml]
2025-07-03 15:33:53.186|[localhost-startStop-1]|INFO|XmlConfigurationProvider|Parsing configuration file [struts.xml]
2025-07-03 15:33:53.187|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.factory.ActionFactory)
2025-07-03 15:33:53.187|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.factory.ResultFactory)
2025-07-03 15:33:53.187|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.factory.ConverterFactory)
2025-07-03 15:33:53.187|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.factory.InterceptorFactory)
2025-07-03 15:33:53.187|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.factory.ValidatorFactory)
2025-07-03 15:33:53.187|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.factory.UnknownHandlerFactory)
2025-07-03 15:33:53.187|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.FileManagerFactory)
2025-07-03 15:33:53.187|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.impl.XWorkConverter)
2025-07-03 15:33:53.187|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.impl.CollectionConverter)
2025-07-03 15:33:53.187|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.impl.ArrayConverter)
2025-07-03 15:33:53.187|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.impl.DateConverter)
2025-07-03 15:33:53.191|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.impl.NumberConverter)
2025-07-03 15:33:53.191|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.impl.StringConverter)
2025-07-03 15:33:53.191|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.ConversionPropertiesProcessor)
2025-07-03 15:33:53.191|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.ConversionFileProcessor)
2025-07-03 15:33:53.191|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.ConversionAnnotationProcessor)
2025-07-03 15:33:53.191|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.TypeConverterCreator)
2025-07-03 15:33:53.191|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.TypeConverterHolder)
2025-07-03 15:33:53.191|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.TextProvider)
2025-07-03 15:33:53.191|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.LocaleProvider)
2025-07-03 15:33:53.191|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.ActionProxyFactory)
2025-07-03 15:33:53.191|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.ObjectTypeDeterminer)
2025-07-03 15:33:53.191|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (org.apache.struts2.dispatcher.mapper.ActionMapper)
2025-07-03 15:33:53.194|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (jakarta) for (org.apache.struts2.dispatcher.multipart.MultiPartRequest)
2025-07-03 15:33:53.194|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (org.apache.struts2.views.freemarker.FreemarkerManager)
2025-07-03 15:33:53.195|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (org.apache.struts2.components.UrlRenderer)
2025-07-03 15:33:53.195|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.validator.ActionValidatorManager)
2025-07-03 15:33:53.195|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.util.ValueStackFactory)
2025-07-03 15:33:53.196|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.util.reflection.ReflectionProvider)
2025-07-03 15:33:53.196|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.util.reflection.ReflectionContextFactory)
2025-07-03 15:33:53.196|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.util.PatternMatcher)
2025-07-03 15:33:53.196|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (org.apache.struts2.util.ContentTypeMatcher)
2025-07-03 15:33:53.196|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (org.apache.struts2.dispatcher.StaticContentLoader)
2025-07-03 15:33:53.196|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.UnknownHandlerManager)
2025-07-03 15:33:53.196|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (org.apache.struts2.views.util.UrlHelper)
2025-07-03 15:33:53.197|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.util.TextParser)
2025-07-03 15:33:53.197|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (org.apache.struts2.dispatcher.DispatcherErrorHandler)
2025-07-03 15:33:53.197|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.security.ExcludedPatternsChecker)
2025-07-03 15:33:53.197|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.security.AcceptedPatternsChecker)
2025-07-03 15:33:53.209|[localhost-startStop-1]|INFO|StrutsSpringObjectFactory|Initializing Struts-Spring integration...
2025-07-03 15:33:53.209|[localhost-startStop-1]|INFO|SpringObjectFactory|Setting autowire strategy to name
2025-07-03 15:33:53.209|[localhost-startStop-1]|INFO|StrutsSpringObjectFactory|... initialized Struts-Spring integration successfully
2025-07-03 15:34:38.736|[http-bio-8080-exec-1]|WARN|ServletUrlRenderer|No configuration found for the specified action: '/login.jsp' in namespace: '/page'. Form action defaulting to 'action' attribute's literal value.
2025-07-03 15:34:38.823|[http-bio-8080-exec-1]|WARN|ServletUrlRenderer|No configuration found for the specified action: '/login.jsp' in namespace: '/page'. Form action defaulting to 'action' attribute's literal value.
2025-07-03 15:51:50.688|[localhost-startStop-1]|INFO|ContextLoader|Root WebApplicationContext: initialization started
2025-07-03 15:51:50.746|[localhost-startStop-1]|INFO|XmlWebApplicationContext|Refreshing Root WebApplicationContext: startup date [Thu Jul 03 15:51:50 CST 2025]; root of context hierarchy
2025-07-03 15:51:50.773|[localhost-startStop-1]|INFO|XmlBeanDefinitionReader|Loading XML bean definitions from file [C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\target\classes\spring-beans.xml]
2025-07-03 15:51:50.943|[localhost-startStop-1]|INFO|XmlBeanDefinitionReader|Loading XML bean definitions from file [C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\target\classes\spring-quartz.xml]
2025-07-03 15:51:51.123|[localhost-startStop-1]|INFO|PropertyPlaceholderConfigurer|Loading properties file from class path resource [datasource.properties]
2025-07-03 15:51:51.125|[localhost-startStop-1]|INFO|PropertyPlaceholderConfigurer|Loading properties file from class path resource [redis.properties]
2025-07-03 15:51:51.125|[localhost-startStop-1]|INFO|PropertyPlaceholderConfigurer|Loading properties file from class path resource [hibernate.properties]
2025-07-03 15:51:51.127|[localhost-startStop-1]|INFO|AutowiredAnnotationBeanPostProcessor|JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2025-07-03 15:51:51.401|[localhost-startStop-1]|INFO|ContextLoader|Root WebApplicationContext: initialization completed in 709 ms
2025-07-03 15:51:51.560|[localhost-startStop-1]|INFO|XmlConfigurationProvider|Parsing configuration file [struts-default.xml]
2025-07-03 15:51:51.607|[localhost-startStop-1]|INFO|XmlConfigurationProvider|Parsing configuration file [struts-plugin.xml]
2025-07-03 15:51:51.637|[localhost-startStop-1]|INFO|XmlConfigurationProvider|Parsing configuration file [struts.xml]
2025-07-03 15:51:51.640|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.factory.ActionFactory)
2025-07-03 15:51:51.640|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.factory.ResultFactory)
2025-07-03 15:51:51.640|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.factory.ConverterFactory)
2025-07-03 15:51:51.640|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.factory.InterceptorFactory)
2025-07-03 15:51:51.640|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.factory.ValidatorFactory)
2025-07-03 15:51:51.641|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.factory.UnknownHandlerFactory)
2025-07-03 15:51:51.642|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.FileManagerFactory)
2025-07-03 15:51:51.642|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.impl.XWorkConverter)
2025-07-03 15:51:51.642|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.impl.CollectionConverter)
2025-07-03 15:51:51.642|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.impl.ArrayConverter)
2025-07-03 15:51:51.642|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.impl.DateConverter)
2025-07-03 15:51:51.642|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.impl.NumberConverter)
2025-07-03 15:51:51.643|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.impl.StringConverter)
2025-07-03 15:51:51.643|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.ConversionPropertiesProcessor)
2025-07-03 15:51:51.643|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.ConversionFileProcessor)
2025-07-03 15:51:51.643|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.ConversionAnnotationProcessor)
2025-07-03 15:51:51.643|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.TypeConverterCreator)
2025-07-03 15:51:51.643|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.TypeConverterHolder)
2025-07-03 15:51:51.644|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.TextProvider)
2025-07-03 15:51:51.644|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.LocaleProvider)
2025-07-03 15:51:51.644|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.ActionProxyFactory)
2025-07-03 15:51:51.644|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.ObjectTypeDeterminer)
2025-07-03 15:51:51.645|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (org.apache.struts2.dispatcher.mapper.ActionMapper)
2025-07-03 15:51:51.645|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (jakarta) for (org.apache.struts2.dispatcher.multipart.MultiPartRequest)
2025-07-03 15:51:51.645|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (org.apache.struts2.views.freemarker.FreemarkerManager)
2025-07-03 15:51:51.647|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (org.apache.struts2.components.UrlRenderer)
2025-07-03 15:51:51.647|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.validator.ActionValidatorManager)
2025-07-03 15:51:51.648|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.util.ValueStackFactory)
2025-07-03 15:51:51.648|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.util.reflection.ReflectionProvider)
2025-07-03 15:51:51.648|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.util.reflection.ReflectionContextFactory)
2025-07-03 15:51:51.648|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.util.PatternMatcher)
2025-07-03 15:51:51.648|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (org.apache.struts2.util.ContentTypeMatcher)
2025-07-03 15:51:51.648|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (org.apache.struts2.dispatcher.StaticContentLoader)
2025-07-03 15:51:51.648|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.UnknownHandlerManager)
2025-07-03 15:51:51.649|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (org.apache.struts2.views.util.UrlHelper)
2025-07-03 15:51:51.649|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.util.TextParser)
2025-07-03 15:51:51.649|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (org.apache.struts2.dispatcher.DispatcherErrorHandler)
2025-07-03 15:51:51.649|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.security.ExcludedPatternsChecker)
2025-07-03 15:51:51.649|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.security.AcceptedPatternsChecker)
2025-07-03 15:51:51.658|[localhost-startStop-1]|INFO|StrutsSpringObjectFactory|Initializing Struts-Spring integration...
2025-07-03 15:51:51.658|[localhost-startStop-1]|INFO|SpringObjectFactory|Setting autowire strategy to name
2025-07-03 15:51:51.658|[localhost-startStop-1]|INFO|StrutsSpringObjectFactory|... initialized Struts-Spring integration successfully
2025-07-03 15:57:28.846|[localhost-startStop-2]|INFO|XmlWebApplicationContext|Closing Root WebApplicationContext: startup date [Thu Jul 03 15:51:50 CST 2025]; root of context hierarchy
2025-07-03 15:57:44.850|[localhost-startStop-1]|INFO|ContextLoader|Root WebApplicationContext: initialization started
2025-07-03 15:57:44.927|[localhost-startStop-1]|INFO|XmlWebApplicationContext|Refreshing Root WebApplicationContext: startup date [Thu Jul 03 15:57:44 CST 2025]; root of context hierarchy
2025-07-03 15:57:44.952|[localhost-startStop-1]|INFO|XmlBeanDefinitionReader|Loading XML bean definitions from file [C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\target\classes\spring-beans.xml]
2025-07-03 15:57:45.164|[localhost-startStop-1]|INFO|XmlBeanDefinitionReader|Loading XML bean definitions from file [C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\target\classes\spring-quartz.xml]
2025-07-03 15:57:45.305|[localhost-startStop-1]|INFO|PropertyPlaceholderConfigurer|Loading properties file from class path resource [datasource.properties]
2025-07-03 15:57:45.305|[localhost-startStop-1]|INFO|PropertyPlaceholderConfigurer|Loading properties file from class path resource [redis.properties]
2025-07-03 15:57:45.306|[localhost-startStop-1]|INFO|PropertyPlaceholderConfigurer|Loading properties file from class path resource [hibernate.properties]
2025-07-03 15:57:45.310|[localhost-startStop-1]|INFO|AutowiredAnnotationBeanPostProcessor|JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2025-07-03 15:57:45.570|[localhost-startStop-1]|INFO|ContextLoader|Root WebApplicationContext: initialization completed in 719 ms
2025-07-03 15:57:45.678|[localhost-startStop-1]|INFO|XmlConfigurationProvider|Parsing configuration file [struts-default.xml]
2025-07-03 15:57:45.723|[localhost-startStop-1]|INFO|XmlConfigurationProvider|Parsing configuration file [struts-plugin.xml]
2025-07-03 15:57:45.756|[localhost-startStop-1]|INFO|XmlConfigurationProvider|Parsing configuration file [struts.xml]
2025-07-03 15:57:45.759|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.factory.ActionFactory)
2025-07-03 15:57:45.759|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.factory.ResultFactory)
2025-07-03 15:57:45.759|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.factory.ConverterFactory)
2025-07-03 15:57:45.760|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.factory.InterceptorFactory)
2025-07-03 15:57:45.760|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.factory.ValidatorFactory)
2025-07-03 15:57:45.760|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.factory.UnknownHandlerFactory)
2025-07-03 15:57:45.760|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.FileManagerFactory)
2025-07-03 15:57:45.760|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.impl.XWorkConverter)
2025-07-03 15:57:45.761|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.impl.CollectionConverter)
2025-07-03 15:57:45.761|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.impl.ArrayConverter)
2025-07-03 15:57:45.761|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.impl.DateConverter)
2025-07-03 15:57:45.761|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.impl.NumberConverter)
2025-07-03 15:57:45.761|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.impl.StringConverter)
2025-07-03 15:57:45.762|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.ConversionPropertiesProcessor)
2025-07-03 15:57:45.762|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.ConversionFileProcessor)
2025-07-03 15:57:45.763|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.ConversionAnnotationProcessor)
2025-07-03 15:57:45.763|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.TypeConverterCreator)
2025-07-03 15:57:45.763|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.TypeConverterHolder)
2025-07-03 15:57:45.763|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.TextProvider)
2025-07-03 15:57:45.763|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.LocaleProvider)
2025-07-03 15:57:45.764|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.ActionProxyFactory)
2025-07-03 15:57:45.764|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.ObjectTypeDeterminer)
2025-07-03 15:57:45.764|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (org.apache.struts2.dispatcher.mapper.ActionMapper)
2025-07-03 15:57:45.764|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (jakarta) for (org.apache.struts2.dispatcher.multipart.MultiPartRequest)
2025-07-03 15:57:45.764|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (org.apache.struts2.views.freemarker.FreemarkerManager)
2025-07-03 15:57:45.765|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (org.apache.struts2.components.UrlRenderer)
2025-07-03 15:57:45.765|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.validator.ActionValidatorManager)
2025-07-03 15:57:45.765|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.util.ValueStackFactory)
2025-07-03 15:57:45.765|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.util.reflection.ReflectionProvider)
2025-07-03 15:57:45.765|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.util.reflection.ReflectionContextFactory)
2025-07-03 15:57:45.766|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.util.PatternMatcher)
2025-07-03 15:57:45.766|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (org.apache.struts2.util.ContentTypeMatcher)
2025-07-03 15:57:45.766|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (org.apache.struts2.dispatcher.StaticContentLoader)
2025-07-03 15:57:45.767|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.UnknownHandlerManager)
2025-07-03 15:57:45.767|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (org.apache.struts2.views.util.UrlHelper)
2025-07-03 15:57:45.767|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.util.TextParser)
2025-07-03 15:57:45.767|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (org.apache.struts2.dispatcher.DispatcherErrorHandler)
2025-07-03 15:57:45.767|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.security.ExcludedPatternsChecker)
2025-07-03 15:57:45.768|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.security.AcceptedPatternsChecker)
2025-07-03 15:57:45.776|[localhost-startStop-1]|INFO|StrutsSpringObjectFactory|Initializing Struts-Spring integration...
2025-07-03 15:57:45.776|[localhost-startStop-1]|INFO|SpringObjectFactory|Setting autowire strategy to name
2025-07-03 15:57:45.776|[localhost-startStop-1]|INFO|StrutsSpringObjectFactory|... initialized Struts-Spring integration successfully
2025-07-03 16:02:22.690|[localhost-startStop-1]|INFO|ContextLoader|Root WebApplicationContext: initialization started
2025-07-03 16:02:22.746|[localhost-startStop-1]|INFO|XmlWebApplicationContext|Refreshing Root WebApplicationContext: startup date [Thu Jul 03 16:02:22 CST 2025]; root of context hierarchy
2025-07-03 16:02:22.773|[localhost-startStop-1]|INFO|XmlBeanDefinitionReader|Loading XML bean definitions from file [C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\target\classes\spring-beans.xml]
2025-07-03 16:02:22.992|[localhost-startStop-1]|INFO|XmlBeanDefinitionReader|Loading XML bean definitions from file [C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\target\classes\spring-quartz.xml]
2025-07-03 16:02:23.139|[localhost-startStop-1]|INFO|PropertyPlaceholderConfigurer|Loading properties file from class path resource [datasource.properties]
2025-07-03 16:02:23.139|[localhost-startStop-1]|INFO|PropertyPlaceholderConfigurer|Loading properties file from class path resource [redis.properties]
2025-07-03 16:02:23.140|[localhost-startStop-1]|INFO|PropertyPlaceholderConfigurer|Loading properties file from class path resource [hibernate.properties]
2025-07-03 16:02:23.144|[localhost-startStop-1]|INFO|AutowiredAnnotationBeanPostProcessor|JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2025-07-03 16:02:23.411|[localhost-startStop-1]|INFO|ContextLoader|Root WebApplicationContext: initialization completed in 720 ms
2025-07-03 16:02:23.576|[localhost-startStop-1]|INFO|XmlConfigurationProvider|Parsing configuration file [struts-default.xml]
2025-07-03 16:02:23.620|[localhost-startStop-1]|INFO|XmlConfigurationProvider|Parsing configuration file [struts-plugin.xml]
2025-07-03 16:02:23.650|[localhost-startStop-1]|INFO|XmlConfigurationProvider|Parsing configuration file [struts.xml]
2025-07-03 16:02:23.652|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.factory.ActionFactory)
2025-07-03 16:02:23.653|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.factory.ResultFactory)
2025-07-03 16:02:23.653|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.factory.ConverterFactory)
2025-07-03 16:02:23.653|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.factory.InterceptorFactory)
2025-07-03 16:02:23.653|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.factory.ValidatorFactory)
2025-07-03 16:02:23.654|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.factory.UnknownHandlerFactory)
2025-07-03 16:02:23.654|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.FileManagerFactory)
2025-07-03 16:02:23.654|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.impl.XWorkConverter)
2025-07-03 16:02:23.654|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.impl.CollectionConverter)
2025-07-03 16:02:23.654|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.impl.ArrayConverter)
2025-07-03 16:02:23.654|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.impl.DateConverter)
2025-07-03 16:02:23.654|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.impl.NumberConverter)
2025-07-03 16:02:23.655|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.impl.StringConverter)
2025-07-03 16:02:23.655|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.ConversionPropertiesProcessor)
2025-07-03 16:02:23.655|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.ConversionFileProcessor)
2025-07-03 16:02:23.655|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.ConversionAnnotationProcessor)
2025-07-03 16:02:23.655|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.TypeConverterCreator)
2025-07-03 16:02:23.655|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.TypeConverterHolder)
2025-07-03 16:02:23.656|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.TextProvider)
2025-07-03 16:02:23.656|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.LocaleProvider)
2025-07-03 16:02:23.656|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.ActionProxyFactory)
2025-07-03 16:02:23.656|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.ObjectTypeDeterminer)
2025-07-03 16:02:23.656|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (org.apache.struts2.dispatcher.mapper.ActionMapper)
2025-07-03 16:02:23.657|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (jakarta) for (org.apache.struts2.dispatcher.multipart.MultiPartRequest)
2025-07-03 16:02:23.657|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (org.apache.struts2.views.freemarker.FreemarkerManager)
2025-07-03 16:02:23.658|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (org.apache.struts2.components.UrlRenderer)
2025-07-03 16:02:23.659|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.validator.ActionValidatorManager)
2025-07-03 16:02:23.659|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.util.ValueStackFactory)
2025-07-03 16:02:23.660|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.util.reflection.ReflectionProvider)
2025-07-03 16:02:23.660|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.util.reflection.ReflectionContextFactory)
2025-07-03 16:02:23.661|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.util.PatternMatcher)
2025-07-03 16:02:23.661|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (org.apache.struts2.util.ContentTypeMatcher)
2025-07-03 16:02:23.661|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (org.apache.struts2.dispatcher.StaticContentLoader)
2025-07-03 16:02:23.661|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.UnknownHandlerManager)
2025-07-03 16:02:23.661|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (org.apache.struts2.views.util.UrlHelper)
2025-07-03 16:02:23.661|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.util.TextParser)
2025-07-03 16:02:23.661|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (org.apache.struts2.dispatcher.DispatcherErrorHandler)
2025-07-03 16:02:23.661|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.security.ExcludedPatternsChecker)
2025-07-03 16:02:23.661|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.security.AcceptedPatternsChecker)
2025-07-03 16:02:23.670|[localhost-startStop-1]|INFO|StrutsSpringObjectFactory|Initializing Struts-Spring integration...
2025-07-03 16:02:23.670|[localhost-startStop-1]|INFO|SpringObjectFactory|Setting autowire strategy to name
2025-07-03 16:02:23.670|[localhost-startStop-1]|INFO|StrutsSpringObjectFactory|... initialized Struts-Spring integration successfully
2025-07-03 16:08:47.358|[localhost-startStop-2]|INFO|XmlWebApplicationContext|Closing Root WebApplicationContext: startup date [Thu Jul 03 16:02:22 CST 2025]; root of context hierarchy
2025-07-03 16:08:58.839|[localhost-startStop-1]|INFO|ContextLoader|Root WebApplicationContext: initialization started
2025-07-03 16:08:58.902|[localhost-startStop-1]|INFO|XmlWebApplicationContext|Refreshing Root WebApplicationContext: startup date [Thu Jul 03 16:08:58 CST 2025]; root of context hierarchy
2025-07-03 16:08:58.929|[localhost-startStop-1]|INFO|XmlBeanDefinitionReader|Loading XML bean definitions from file [C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\target\classes\spring-beans.xml]
2025-07-03 16:08:59.188|[localhost-startStop-1]|INFO|XmlBeanDefinitionReader|Loading XML bean definitions from file [C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\target\classes\spring-quartz.xml]
2025-07-03 16:08:59.340|[localhost-startStop-1]|INFO|PropertyPlaceholderConfigurer|Loading properties file from class path resource [datasource.properties]
2025-07-03 16:08:59.340|[localhost-startStop-1]|INFO|PropertyPlaceholderConfigurer|Loading properties file from class path resource [redis.properties]
2025-07-03 16:08:59.340|[localhost-startStop-1]|INFO|PropertyPlaceholderConfigurer|Loading properties file from class path resource [hibernate.properties]
2025-07-03 16:08:59.342|[localhost-startStop-1]|INFO|AutowiredAnnotationBeanPostProcessor|JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2025-07-03 16:08:59.602|[localhost-startStop-1]|INFO|ContextLoader|Root WebApplicationContext: initialization completed in 759 ms
2025-07-03 16:08:59.711|[localhost-startStop-1]|INFO|XmlConfigurationProvider|Parsing configuration file [struts-default.xml]
2025-07-03 16:08:59.753|[localhost-startStop-1]|INFO|XmlConfigurationProvider|Parsing configuration file [struts-plugin.xml]
2025-07-03 16:08:59.780|[localhost-startStop-1]|INFO|XmlConfigurationProvider|Parsing configuration file [struts.xml]
2025-07-03 16:08:59.783|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.factory.ActionFactory)
2025-07-03 16:08:59.783|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.factory.ResultFactory)
2025-07-03 16:08:59.783|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.factory.ConverterFactory)
2025-07-03 16:08:59.783|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.factory.InterceptorFactory)
2025-07-03 16:08:59.784|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.factory.ValidatorFactory)
2025-07-03 16:08:59.784|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.factory.UnknownHandlerFactory)
2025-07-03 16:08:59.784|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.FileManagerFactory)
2025-07-03 16:08:59.784|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.impl.XWorkConverter)
2025-07-03 16:08:59.785|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.impl.CollectionConverter)
2025-07-03 16:08:59.785|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.impl.ArrayConverter)
2025-07-03 16:08:59.785|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.impl.DateConverter)
2025-07-03 16:08:59.785|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.impl.NumberConverter)
2025-07-03 16:08:59.786|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.impl.StringConverter)
2025-07-03 16:08:59.786|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.ConversionPropertiesProcessor)
2025-07-03 16:08:59.786|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.ConversionFileProcessor)
2025-07-03 16:08:59.786|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.ConversionAnnotationProcessor)
2025-07-03 16:08:59.786|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.TypeConverterCreator)
2025-07-03 16:08:59.786|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.TypeConverterHolder)
2025-07-03 16:08:59.787|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.TextProvider)
2025-07-03 16:08:59.787|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.LocaleProvider)
2025-07-03 16:08:59.787|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.ActionProxyFactory)
2025-07-03 16:08:59.787|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.ObjectTypeDeterminer)
2025-07-03 16:08:59.787|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (org.apache.struts2.dispatcher.mapper.ActionMapper)
2025-07-03 16:08:59.788|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (jakarta) for (org.apache.struts2.dispatcher.multipart.MultiPartRequest)
2025-07-03 16:08:59.788|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (org.apache.struts2.views.freemarker.FreemarkerManager)
2025-07-03 16:08:59.789|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (org.apache.struts2.components.UrlRenderer)
2025-07-03 16:08:59.789|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.validator.ActionValidatorManager)
2025-07-03 16:08:59.789|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.util.ValueStackFactory)
2025-07-03 16:08:59.790|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.util.reflection.ReflectionProvider)
2025-07-03 16:08:59.790|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.util.reflection.ReflectionContextFactory)
2025-07-03 16:08:59.790|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.util.PatternMatcher)
2025-07-03 16:08:59.790|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (org.apache.struts2.util.ContentTypeMatcher)
2025-07-03 16:08:59.790|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (org.apache.struts2.dispatcher.StaticContentLoader)
2025-07-03 16:08:59.791|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.UnknownHandlerManager)
2025-07-03 16:08:59.791|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (org.apache.struts2.views.util.UrlHelper)
2025-07-03 16:08:59.791|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.util.TextParser)
2025-07-03 16:08:59.791|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (org.apache.struts2.dispatcher.DispatcherErrorHandler)
2025-07-03 16:08:59.791|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.security.ExcludedPatternsChecker)
2025-07-03 16:08:59.792|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.security.AcceptedPatternsChecker)
2025-07-03 16:08:59.801|[localhost-startStop-1]|INFO|StrutsSpringObjectFactory|Initializing Struts-Spring integration...
2025-07-03 16:08:59.802|[localhost-startStop-1]|INFO|SpringObjectFactory|Setting autowire strategy to name
2025-07-03 16:08:59.803|[localhost-startStop-1]|INFO|StrutsSpringObjectFactory|... initialized Struts-Spring integration successfully
2025-07-03 16:33:42.290|[localhost-startStop-1]|INFO|ContextLoader|Root WebApplicationContext: initialization started
2025-07-03 16:33:42.346|[localhost-startStop-1]|INFO|XmlWebApplicationContext|Refreshing Root WebApplicationContext: startup date [Thu Jul 03 16:33:42 CST 2025]; root of context hierarchy
2025-07-03 16:33:42.371|[localhost-startStop-1]|INFO|XmlBeanDefinitionReader|Loading XML bean definitions from file [C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\target\classes\spring-beans.xml]
2025-07-03 16:33:42.553|[localhost-startStop-1]|INFO|XmlBeanDefinitionReader|Loading XML bean definitions from file [C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\target\classes\spring-quartz.xml]
2025-07-03 16:33:42.690|[localhost-startStop-1]|INFO|PropertyPlaceholderConfigurer|Loading properties file from class path resource [datasource.properties]
2025-07-03 16:33:42.690|[localhost-startStop-1]|INFO|PropertyPlaceholderConfigurer|Loading properties file from class path resource [redis.properties]
2025-07-03 16:33:42.690|[localhost-startStop-1]|INFO|PropertyPlaceholderConfigurer|Loading properties file from class path resource [hibernate.properties]
2025-07-03 16:33:42.694|[localhost-startStop-1]|INFO|AutowiredAnnotationBeanPostProcessor|JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2025-07-03 16:33:42.950|[localhost-startStop-1]|INFO|ContextLoader|Root WebApplicationContext: initialization completed in 660 ms
2025-07-03 16:33:43.135|[localhost-startStop-1]|INFO|XmlConfigurationProvider|Parsing configuration file [struts-default.xml]
2025-07-03 16:33:43.176|[localhost-startStop-1]|INFO|XmlConfigurationProvider|Parsing configuration file [struts-plugin.xml]
2025-07-03 16:33:43.209|[localhost-startStop-1]|INFO|XmlConfigurationProvider|Parsing configuration file [struts.xml]
2025-07-03 16:33:43.212|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.factory.ActionFactory)
2025-07-03 16:33:43.212|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.factory.ResultFactory)
2025-07-03 16:33:43.212|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.factory.ConverterFactory)
2025-07-03 16:33:43.213|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.factory.InterceptorFactory)
2025-07-03 16:33:43.213|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.factory.ValidatorFactory)
2025-07-03 16:33:43.213|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.factory.UnknownHandlerFactory)
2025-07-03 16:33:43.214|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.FileManagerFactory)
2025-07-03 16:33:43.214|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.impl.XWorkConverter)
2025-07-03 16:33:43.214|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.impl.CollectionConverter)
2025-07-03 16:33:43.214|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.impl.ArrayConverter)
2025-07-03 16:33:43.214|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.impl.DateConverter)
2025-07-03 16:33:43.215|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.impl.NumberConverter)
2025-07-03 16:33:43.216|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.impl.StringConverter)
2025-07-03 16:33:43.216|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.ConversionPropertiesProcessor)
2025-07-03 16:33:43.216|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.ConversionFileProcessor)
2025-07-03 16:33:43.216|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.ConversionAnnotationProcessor)
2025-07-03 16:33:43.216|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.TypeConverterCreator)
2025-07-03 16:33:43.216|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.TypeConverterHolder)
2025-07-03 16:33:43.217|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.TextProvider)
2025-07-03 16:33:43.217|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.LocaleProvider)
2025-07-03 16:33:43.217|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.ActionProxyFactory)
2025-07-03 16:33:43.217|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.ObjectTypeDeterminer)
2025-07-03 16:33:43.217|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (org.apache.struts2.dispatcher.mapper.ActionMapper)
2025-07-03 16:33:43.217|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (jakarta) for (org.apache.struts2.dispatcher.multipart.MultiPartRequest)
2025-07-03 16:33:43.218|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (org.apache.struts2.views.freemarker.FreemarkerManager)
2025-07-03 16:33:43.219|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (org.apache.struts2.components.UrlRenderer)
2025-07-03 16:33:43.219|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.validator.ActionValidatorManager)
2025-07-03 16:33:43.219|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.util.ValueStackFactory)
2025-07-03 16:33:43.219|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.util.reflection.ReflectionProvider)
2025-07-03 16:33:43.219|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.util.reflection.ReflectionContextFactory)
2025-07-03 16:33:43.219|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.util.PatternMatcher)
2025-07-03 16:33:43.219|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (org.apache.struts2.util.ContentTypeMatcher)
2025-07-03 16:33:43.220|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (org.apache.struts2.dispatcher.StaticContentLoader)
2025-07-03 16:33:43.220|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.UnknownHandlerManager)
2025-07-03 16:33:43.220|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (org.apache.struts2.views.util.UrlHelper)
2025-07-03 16:33:43.220|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.util.TextParser)
2025-07-03 16:33:43.220|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (org.apache.struts2.dispatcher.DispatcherErrorHandler)
2025-07-03 16:33:43.221|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.security.ExcludedPatternsChecker)
2025-07-03 16:33:43.221|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.security.AcceptedPatternsChecker)
2025-07-03 16:33:43.230|[localhost-startStop-1]|INFO|StrutsSpringObjectFactory|Initializing Struts-Spring integration...
2025-07-03 16:33:43.232|[localhost-startStop-1]|INFO|SpringObjectFactory|Setting autowire strategy to name
2025-07-03 16:33:43.232|[localhost-startStop-1]|INFO|StrutsSpringObjectFactory|... initialized Struts-Spring integration successfully
2025-07-03 16:34:24.143|[http-bio-8080-exec-1]|WARN|ServletUrlRenderer|No configuration found for the specified action: '/login.jsp' in namespace: '/page'. Form action defaulting to 'action' attribute's literal value.
2025-07-03 16:34:24.232|[http-bio-8080-exec-1]|WARN|ServletUrlRenderer|No configuration found for the specified action: '/login.jsp' in namespace: '/page'. Form action defaulting to 'action' attribute's literal value.
2025-07-03 16:37:21.433|[localhost-startStop-2]|INFO|XmlWebApplicationContext|Closing Root WebApplicationContext: startup date [Thu Jul 03 16:33:42 CST 2025]; root of context hierarchy
2025-07-03 16:37:38.895|[localhost-startStop-1]|INFO|ContextLoader|Root WebApplicationContext: initialization started
2025-07-03 16:37:38.958|[localhost-startStop-1]|INFO|XmlWebApplicationContext|Refreshing Root WebApplicationContext: startup date [Thu Jul 03 16:37:38 CST 2025]; root of context hierarchy
2025-07-03 16:37:38.993|[localhost-startStop-1]|INFO|XmlBeanDefinitionReader|Loading XML bean definitions from file [C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\target\classes\spring-beans.xml]
2025-07-03 16:37:39.208|[localhost-startStop-1]|INFO|XmlBeanDefinitionReader|Loading XML bean definitions from file [C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\target\classes\spring-quartz.xml]
2025-07-03 16:37:39.358|[localhost-startStop-1]|INFO|PropertyPlaceholderConfigurer|Loading properties file from class path resource [datasource.properties]
2025-07-03 16:37:39.358|[localhost-startStop-1]|INFO|PropertyPlaceholderConfigurer|Loading properties file from class path resource [redis.properties]
2025-07-03 16:37:39.358|[localhost-startStop-1]|INFO|PropertyPlaceholderConfigurer|Loading properties file from class path resource [hibernate.properties]
2025-07-03 16:37:39.361|[localhost-startStop-1]|INFO|AutowiredAnnotationBeanPostProcessor|JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
2025-07-03 16:37:39.657|[localhost-startStop-1]|INFO|ContextLoader|Root WebApplicationContext: initialization completed in 761 ms
2025-07-03 16:37:39.840|[localhost-startStop-1]|INFO|XmlConfigurationProvider|Parsing configuration file [struts-default.xml]
2025-07-03 16:37:39.886|[localhost-startStop-1]|INFO|XmlConfigurationProvider|Parsing configuration file [struts-plugin.xml]
2025-07-03 16:37:39.923|[localhost-startStop-1]|INFO|XmlConfigurationProvider|Parsing configuration file [struts.xml]
2025-07-03 16:37:39.925|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.factory.ActionFactory)
2025-07-03 16:37:39.926|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.factory.ResultFactory)
2025-07-03 16:37:39.926|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.factory.ConverterFactory)
2025-07-03 16:37:39.926|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.factory.InterceptorFactory)
2025-07-03 16:37:39.926|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.factory.ValidatorFactory)
2025-07-03 16:37:39.926|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.factory.UnknownHandlerFactory)
2025-07-03 16:37:39.927|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.FileManagerFactory)
2025-07-03 16:37:39.927|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.impl.XWorkConverter)
2025-07-03 16:37:39.928|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.impl.CollectionConverter)
2025-07-03 16:37:39.928|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.impl.ArrayConverter)
2025-07-03 16:37:39.928|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.impl.DateConverter)
2025-07-03 16:37:39.928|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.impl.NumberConverter)
2025-07-03 16:37:39.928|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.impl.StringConverter)
2025-07-03 16:37:39.928|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.ConversionPropertiesProcessor)
2025-07-03 16:37:39.928|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.ConversionFileProcessor)
2025-07-03 16:37:39.928|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.ConversionAnnotationProcessor)
2025-07-03 16:37:39.930|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.TypeConverterCreator)
2025-07-03 16:37:39.930|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.TypeConverterHolder)
2025-07-03 16:37:39.930|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.TextProvider)
2025-07-03 16:37:39.931|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.LocaleProvider)
2025-07-03 16:37:39.931|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.ActionProxyFactory)
2025-07-03 16:37:39.931|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.conversion.ObjectTypeDeterminer)
2025-07-03 16:37:39.931|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (org.apache.struts2.dispatcher.mapper.ActionMapper)
2025-07-03 16:37:39.931|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (jakarta) for (org.apache.struts2.dispatcher.multipart.MultiPartRequest)
2025-07-03 16:37:39.931|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (org.apache.struts2.views.freemarker.FreemarkerManager)
2025-07-03 16:37:39.933|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (org.apache.struts2.components.UrlRenderer)
2025-07-03 16:37:39.934|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.validator.ActionValidatorManager)
2025-07-03 16:37:39.934|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.util.ValueStackFactory)
2025-07-03 16:37:39.934|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.util.reflection.ReflectionProvider)
2025-07-03 16:37:39.934|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.util.reflection.ReflectionContextFactory)
2025-07-03 16:37:39.934|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.util.PatternMatcher)
2025-07-03 16:37:39.934|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (org.apache.struts2.util.ContentTypeMatcher)
2025-07-03 16:37:39.934|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (org.apache.struts2.dispatcher.StaticContentLoader)
2025-07-03 16:37:39.934|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.UnknownHandlerManager)
2025-07-03 16:37:39.934|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (org.apache.struts2.views.util.UrlHelper)
2025-07-03 16:37:39.934|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.util.TextParser)
2025-07-03 16:37:39.934|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (org.apache.struts2.dispatcher.DispatcherErrorHandler)
2025-07-03 16:37:39.934|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.security.ExcludedPatternsChecker)
2025-07-03 16:37:39.936|[localhost-startStop-1]|INFO|AbstractBeanSelectionProvider|Choosing bean (struts) for (com.opensymphony.xwork2.security.AcceptedPatternsChecker)
2025-07-03 16:37:39.947|[localhost-startStop-1]|INFO|StrutsSpringObjectFactory|Initializing Struts-Spring integration...
2025-07-03 16:37:39.948|[localhost-startStop-1]|INFO|SpringObjectFactory|Setting autowire strategy to name
2025-07-03 16:37:39.948|[localhost-startStop-1]|INFO|StrutsSpringObjectFactory|... initialized Struts-Spring integration successfully
2025-07-03 16:38:14.591|[http-bio-8080-exec-5]|WARN|ServletUrlRenderer|No configuration found for the specified action: '/login.jsp' in namespace: '/page'. Form action defaulting to 'action' attribute's literal value.
2025-07-03 16:38:14.678|[http-bio-8080-exec-5]|WARN|ServletUrlRenderer|No configuration found for the specified action: '/login.jsp' in namespace: '/page'. Form action defaulting to 'action' attribute's literal value.
