@echo off
echo ========================================
echo Maven项目构建脚本 - 依赖问题修复版
echo ========================================
echo.

echo 0. 强制更新依赖...
mvn dependency:purge-local-repository -DmanualInclude="org.hibernate:hibernate-commons-annotations,mysql:mysql-connector-java"
echo.

echo 1. 验证项目配置...
mvn validate
if %errorlevel% neq 0 (
    echo 项目配置验证失败！
    echo 尝试使用备用仓库...
    mvn validate -Dmaven.repo.remote=https://repo1.maven.org/maven2,https://maven.aliyun.com/repository/public
    if %errorlevel% neq 0 (
        echo 项目配置验证仍然失败！请检查网络连接和Maven配置
        pause
        exit /b 1
    )
)
echo 项目配置验证成功！
echo.

echo 2. 清理项目...
mvn clean
if %errorlevel% neq 0 (
    echo 项目清理失败！
    pause
    exit /b 1
)
echo 项目清理成功！
echo.

echo 3. 下载依赖...
mvn dependency:resolve
if %errorlevel% neq 0 (
    echo 依赖下载失败！尝试使用备用仓库...
    mvn dependency:resolve -Dmaven.repo.remote=https://repo1.maven.org/maven2,https://maven.aliyun.com/repository/public
)
echo.

echo 4. 编译项目...
mvn compile
if %errorlevel% neq 0 (
    echo 项目编译失败！请检查源代码是否已恢复到src/main/java目录
    echo 如果是依赖问题，请检查网络连接或尝试手动下载缺失的依赖
    pause
    exit /b 1
)
echo 项目编译成功！
echo.

echo 5. 运行测试...
mvn test
if %errorlevel% neq 0 (
    echo 测试运行失败！
    echo 注意：如果没有测试代码，这是正常的
)
echo.

echo 6. 打包项目...
mvn package
if %errorlevel% neq 0 (
    echo 项目打包失败！
    pause
    exit /b 1
)
echo 项目打包成功！WAR文件位于target目录
echo.

echo ========================================
echo 构建完成！
echo ========================================
echo 生成的WAR文件: target\808FrontProject.war
echo 可以部署到Tomcat服务器
echo.
echo 如需启动内置Tomcat服务器，请运行:
echo mvn tomcat7:run
echo.
pause
