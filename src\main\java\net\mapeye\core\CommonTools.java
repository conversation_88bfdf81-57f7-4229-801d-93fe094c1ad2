package net.mapeye.core;

import java.util.Calendar;

/**
 * Common utility class for date processing and formatting
 *
 * <AUTHOR>
 * @date 2025-07-03
 */
public class CommonTools {

    /**
     * Get the number of days in a month
     * @param month month (1-12)
     * @return number of days in the month
     */
    public static int getDays(int month) {
        // Use current year for calculation, can pass year parameter if leap year consideration needed
        Calendar calendar = Calendar.getInstance();
        int year = calendar.get(Calendar.YEAR);
        return getDays(year, month);
    }

    /**
     * Get the number of days in a specific year and month
     * @param year year
     * @param month month (1-12)
     * @return number of days in the month
     */
    public static int getDays(int year, int month) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR, year);
        calendar.set(Calendar.MONTH, month - 1); // Calendar month starts from 0
        return calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
    }

    /**
     * Get normalized month string (two digits format)
     * @param month month (1-12)
     * @return formatted month string (01-12)
     */
    public static String getMonthNormal(int month) {
        if (month < 1 || month > 12) {
            throw new IllegalArgumentException("Month must be between 1-12");
        }
        return String.format("%02d", month);
    }

    /**
     * Get normalized day string (two digits format)
     * @param day day (1-31)
     * @return formatted day string (01-31)
     */
    public static String getDayNormalOfMonth(int day) {
        if (day < 1 || day > 31) {
            throw new IllegalArgumentException("Day must be between 1-31");
        }
        return String.format("%02d", day);
    }

    /**
     * Get normalized date string
     * @param year year
     * @param month month (1-12)
     * @param day day (1-31)
     * @param separator separator
     * @return formatted date string
     */
    public static String getDateNormal(int year, int month, int day, String separator) {
        // Handle date overflow
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR, year);
        calendar.set(Calendar.MONTH, month - 1); // Calendar month starts from 0
        calendar.set(Calendar.DAY_OF_MONTH, day);

        int actualYear = calendar.get(Calendar.YEAR);
        int actualMonth = calendar.get(Calendar.MONTH) + 1; // Convert back to 1-12
        int actualDay = calendar.get(Calendar.DAY_OF_MONTH);

        return String.format("%04d%s%02d%s%02d",
            actualYear, separator, actualMonth, separator, actualDay);
    }

    /**
     * Get normalized date string (default using "-" as separator)
     * @param year year
     * @param month month (1-12)
     * @param day day (1-31)
     * @return formatted date string
     */
    public static String getDateNormal(int year, int month, int day) {
        return getDateNormal(year, month, day, "-");
    }

    /**
     * Get normalized year string (four digits format)
     * @param year year
     * @return formatted year string
     */
    public static String getYearNormal(int year) {
        return String.format("%04d", year);
    }

    /**
     * Get normalized hour string (two digits format)
     * @param hour hour (0-23)
     * @return formatted hour string (00-23)
     */
    public static String getHourNormal(int hour) {
        if (hour < 0 || hour > 23) {
            throw new IllegalArgumentException("Hour must be between 0-23");
        }
        return String.format("%02d", hour);
    }

    /**
     * Get normalized minute string (two digits format)
     * @param minute minute (0-59)
     * @return formatted minute string (00-59)
     */
    public static String getMinuteNormal(int minute) {
        if (minute < 0 || minute > 59) {
            throw new IllegalArgumentException("Minute must be between 0-59");
        }
        return String.format("%02d", minute);
    }

    /**
     * Get normalized second string (two digits format)
     * @param second second (0-59)
     * @return formatted second string (00-59)
     */
    public static String getSecondNormal(int second) {
        if (second < 0 || second > 59) {
            throw new IllegalArgumentException("Second must be between 0-59");
        }
        return String.format("%02d", second);
    }

    /**
     * Get normalized time string
     * @param hour hour (0-23)
     * @param minute minute (0-59)
     * @param second second (0-59)
     * @param separator separator
     * @return formatted time string
     */
    public static String getTimeNormal(int hour, int minute, int second, String separator) {
        return String.format("%02d%s%02d%s%02d", hour, separator, minute, separator, second);
    }

    /**
     * Get normalized time string (default using ":" as separator)
     * @param hour hour (0-23)
     * @param minute minute (0-59)
     * @param second second (0-59)
     * @return formatted time string
     */
    public static String getTimeNormal(int hour, int minute, int second) {
        return getTimeNormal(hour, minute, second, ":");
    }

    /**
     * Get normalized datetime string
     * @param year year
     * @param month month (1-12)
     * @param day day (1-31)
     * @param hour hour (0-23)
     * @param minute minute (0-59)
     * @param second second (0-59)
     * @return formatted datetime string
     */
    public static String getDateTimeNormal(int year, int month, int day, int hour, int minute, int second) {
        return getDateNormal(year, month, day) + " " + getTimeNormal(hour, minute, second);
    }

    /**
     * Check if year is leap year
     * @param year year
     * @return whether it is leap year
     */
    public static boolean isLeapYear(int year) {
        return (year % 4 == 0 && year % 100 != 0) || (year % 400 == 0);
    }

    /**
     * Validate if date is valid
     * @param year year
     * @param month month (1-12)
     * @param day day (1-31)
     * @return whether date is valid
     */
    public static boolean isValidDate(int year, int month, int day) {
        if (month < 1 || month > 12) {
            return false;
        }
        if (day < 1) {
            return false;
        }
        int maxDays = getDays(year, month);
        return day <= maxDays;
    }

    /**
     * Validate if time is valid
     * @param hour hour (0-23)
     * @param minute minute (0-59)
     * @param second second (0-59)
     * @return whether time is valid
     */
    public static boolean isValidTime(int hour, int minute, int second) {
        return hour >= 0 && hour <= 23 &&
               minute >= 0 && minute <= 59 &&
               second >= 0 && second <= 59;
    }
}
