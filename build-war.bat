@echo off
chcp 65001 >nul
echo ========================================
echo Maven WAR Package Build Script
echo ========================================
echo.

echo Step 1: Clean previous build...
mvn clean
if %errorlevel% neq 0 (
    echo ERROR: Clean failed!
    pause
    exit /b 1
)
echo Clean completed successfully!
echo.

echo Step 2: Compile source code...
mvn compile
if %errorlevel% neq 0 (
    echo ERROR: Compilation failed!
    echo Please check if source code is in src/main/java directory
    pause
    exit /b 1
)
echo Compilation completed successfully!
echo.

echo Step 3: Package WAR file...
mvn package -DskipTests
if %errorlevel% neq 0 (
    echo ERROR: WAR packaging failed!
    pause
    exit /b 1
)
echo.

echo ========================================
echo BUILD SUCCESSFUL!
echo ========================================
echo.
echo WAR file location: target\808FrontProject.war
echo File size:
dir target\808FrontProject.war | findstr "808FrontProject.war"
echo.

echo You can now:
echo 1. Deploy to Tomcat: Copy target\808FrontProject.war to Tomcat webapps folder
echo 2. Test locally: mvn tomcat7:run
echo 3. Check WAR contents: jar -tf target\808FrontProject.war
echo.

echo Press any key to continue...
pause >nul
