# Maven
target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties
.mvn/wrapper/maven-wrapper.jar

# Compiled class files
*.class

# Log files
*.log
logs/

# BlueJ files
*.ctxt

# Mobile Tools for Java (J2ME)
.mtj.tmp/

# Package Files
*.jar
*.war
*.nar
*.ear
*.zip
*.tar.gz
*.rar

# Virtual machine crash logs
hs_err_pid*
replay_pid*

# IDE files
## IntelliJ IDEA
.idea/
*.iws
*.iml
*.ipr

## Eclipse
.project
.classpath
.c9/
*.launch
.settings/
.metadata
bin/
tmp/
*.tmp
*.bak
*.swp
*~.nib
local.properties
.settings/
.loadpath
.recommenders

## NetBeans
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/

## VS Code
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# OS generated files
## Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msm
*.msp
*.lnk

## macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

## Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# Application specific
## Tomcat
tomcat/
.tomcat/

## Database
*.db
*.sqlite
*.sqlite3

## Configuration files with sensitive data
application-local.properties
application-dev.properties
database.properties
jdbc.properties

# Build tools
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Temporary files
*.tmp
*.temp
*~

# Backup files
*.bak
*.backup
*.old

# Test coverage reports
coverage/
*.lcov

# Spring Boot
spring-boot-*.log
application-*.log

# Hibernate
hibernate.cfg.xml.local

# Redis
dump.rdb

# Custom project files
# Add any project-specific files or directories that should be ignored
