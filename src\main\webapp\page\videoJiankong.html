<!DOCTYPE html>
 



<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频监控</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            width: 100vw;
            height: 100vh;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .tag {
            height: 35px;
            background-color: #1a3e72;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 10px;
            font-size: 15px;
            color: #FFF;
        }

        .tag-left {
            margin-left: 20px;
            display: flex;
            gap: 20px;
        }

        .controls {
            height: 25px;
            background-color: #EEE;
            display: flex;
            justify-content: flex-end;
            align-items: center;
            padding-right: 10px;
            font-family: Arial, sans-serif;
            border-top: 1px solid #ddd;
            border-bottom: 1px solid #ddd;
            gap: 30px;
        }

        .controls>div {
            margin-left: 0;
            cursor: pointer;
        }

        .grid-container {
            flex: 1;
            display: grid;
            grid-template-rows: repeat(3, 1fr);
            grid-template-columns: repeat(3, 1fr);
            gap: 3px;
            background-color: #444;
            overflow: auto;
        }

        .grid-item {
            background-color: #000;
            position: relative; /* 关键：使子元素的绝对定位基于此 */
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
            min-width: 0; /* 重要：允许内容收缩 */
            min-height: 0; /* 重要：允许内容收缩 */
        }

        .video-container {
            width: 100%;
            height: 100%;
            position: relative;
            background-color: #000;
            border: 2px solid #333;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            min-width: 0; /* 重要：允许收缩 */
            min-height: 0; /* 重要：允许收缩 */
        }

        .video-header {
            background-color: #333;
            color: white;
            padding: 8px 12px;
            font-size: 14px;
            font-weight: bold;
            text-align: center;
            height: 20px;
            line-height: 20px;
        }

        .video-box {
            width: 100%;
            height: calc(100% - 36px); /* 减去header高度 */
            position: relative;
        }

        .demo-video {
            width: 100%;
            height: 100%;
            object-fit: fill;
            background-color: #000;
            display: block;
        }

        .demo-video::-webkit-media-controls-play-button {
            display: none;
        }

        .demo-video::-webkit-media-controls-current-time-display {
            display: none;
        }

        .demo-video::-webkit-media-controls-timeline {
            display: none;
        }

        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 14px;
            color: #fff;
            background-color: rgba(0, 0, 0, 0.8);
            padding: 8px 16px;
            border-radius: 4px;
            text-align: center;
        }

        .video-stream {
            /*max-width: 100%;
            max-height: 100%;
            */
            object-fit: fill; /*fill为强制拉伸，contain为等比缩放*/
            position: relative;
            z-index: 0; /* 图片层级低于信息框 */
            width: 100%; /* 关键：宽度填充容器 */
            height: 100%; /* 关键：高度填充容器 */
            display: block; /* 避免inline元素的间隙问题 */
        }

        /* 信息框样式 */
        .info-left {
            position: absolute;
            left: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            color: white;
            padding: 2px 5px;
            font-size: 12px;
            border-top-right-radius: 3px;
            z-index: 2; /* 高于图片 */
        }

        .info-right {
            position: absolute;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            color: white;
            padding: 2px 5px;
            font-size: 12px;
            border-top-left-radius: 3px;
            z-index: 2; /* 高于图片 */
        }

        .controls input {
            width: 50px;
            margin: 0 5px;
        }

        .controls button {
            padding: 2px 5px;
            margin-left: 5px;
        }

        .exit-fullscreen {
            position: fixed;
            top: 10px;
            right: 10px;
            z-index: 9999;
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 5px 10px;
            border-radius: 5px;
            cursor: pointer;
            display: none;
        }
        /* 新增的<a>标签交互样式 */
            a {
            color: black;
            text-decoration: none;
            transition: font-weight 0.2s ease;
        }

        a:hover {
            color: black;
            text-decoration: underline;
        }
    </style>
    <!-- 提前加载html2canvas库 -->
    <script src="https://html2canvas.hertzen.com/dist/html2canvas.min.js"></script>
    <!-- 引入 flv.js 库 -->
    <script src="../js/flv.js"></script>
    <script>
    // 获取当前页面的URL参数
    const urlParams = new URLSearchParams(window.location.search);
    const carID = urlParams.get('carID'); // 返回 "qqq"

    var initialIndex=0;
    var iterindex=0;
    var gridNumber1=2;
    var gridNumber=3;

    var videoCheck=null;

    // FLV视频播放器配置
    const host = "*************";
    const port = 8002;

    // VideoPlayer类 - 来自test-all.html
    class VideoPlayer {
        constructor(channel) {
            this.channel = channel;
            this.player = null;
            this.loading = true;
            this.videoElement = document.getElementById(`player${channel}`);
            this.loadingElement = document.getElementById(`loading${channel}`);
            this.rtspUrl = `ws://${host}:${port}/rtsp?deviceId=16065442039&channel=${channel}`;
            //this.rtspUrl = `ws://${host}:${port}/rtsp?deviceId=${carID}&channel=${channel}`;

            this.init();
        }

        init() {
            // 绑定双击全屏事件
            if (this.videoElement) {
                this.videoElement.addEventListener('dblclick', () => {
                    this.fullScreen();
                });
            }

            // 等待 flv.js 加载完成后播放视频
            this.waitForFlvjs();

            // 页面卸载时清理资源
            window.addEventListener('beforeunload', () => {
                this.cleanup();
            });
        }

        waitForFlvjs() {
            // 检查 flv.js 是否已加载
            if (typeof flvjs !== 'undefined') {
                this.playVideo();
            } else {
                // 如果还没加载，等待 100ms 后重试
                setTimeout(() => {
                    this.waitForFlvjs();
                }, 100);
            }
        }

        fullScreen() {
            const video = this.videoElement;
            if (video && video.requestFullscreen) {
                video.requestFullscreen();
            } else if (video && video.mozRequestFullScreen) {
                video.mozRequestFullScreen();
            } else if (video && video.webkitRequestFullScreen) {
                video.webkitRequestFullScreen();
            } else if (video && video.msRequestFullscreen) {
                video.msRequestFullscreen();
            }
        }

        playVideo() {
            const time1 = new Date().getTime();

            if (typeof flvjs !== 'undefined' && flvjs.isSupported()) {
                const video = this.videoElement;

                if (video) {
                    // 如果已有播放器，先清理
                    if (this.player) {
                        this.player.unload();
                        this.player.destroy();
                        this.player = null;
                        this.loading = true;
                        if (this.loadingElement) {
                            this.loadingElement.style.display = 'block';
                        }
                    }

                    // 创建新的播放器
                    this.player = flvjs.createPlayer({
                        type: 'flv',
                        isLive: true,
                        url: this.rtspUrl
                    });

                    this.player.attachMediaElement(video);

                    try {
                        this.player.load();
                        this.player.play().then(() => {
                            console.log(`通道${this.channel}播放开始，耗时:`, new Date().getTime() - time1, 'ms');
                            this.loading = false;
                            if (this.loadingElement) {
                                this.loadingElement.style.display = 'none';
                            }
                        }).catch((error) => {
                            console.error(`通道${this.channel}播放失败:`, error);
                            if (this.loadingElement) {
                                this.loadingElement.textContent = '播放失败';
                            }
                        });
                    } catch (error) {
                        console.error(`通道${this.channel}加载失败:`, error);
                        if (this.loadingElement) {
                            this.loadingElement.textContent = '加载失败';
                        }
                    }
                }
            } else {
                console.error('当前浏览器不支持 FLV.js 或 FLV.js 未加载');
                if (this.loadingElement) {
                    this.loadingElement.textContent = '当前浏览器不支持 FLV 播放';
                }
            }
        }

        // 重新播放（用于重连等场景）
        replay() {
            this.playVideo();
        }

        // 清理资源
        cleanup() {
            if (this.player) {
                this.player.unload();
                this.player.destroy();
                this.player = null;
            }
        }
    }

    // 全局变量存储播放器实例
    let videoPlayers = {};
        // 初始化网格
        function createGrid(rows, cols) {
            const container = document.getElementById('gridContainer');
            container.innerHTML = '';

            container.style.gridTemplateRows = `repeat(${rows}, 1fr)`;
            container.style.gridTemplateColumns = `repeat(${cols}, 1fr)`;

            for (let i = 0; i < rows * cols; i++) {
                // 创建video-container结构（来自test-all.html）
                const videoContainer = document.createElement('div');
                videoContainer.className = 'video-container';

                // 创建video-header
                const videoHeader = document.createElement('div');
                videoHeader.className = 'video-header';
                videoHeader.textContent = `通道 ${i + 1}`;

                // 创建video-box
                const videoBox = document.createElement('div');
                videoBox.className = 'video-box';

                // 创建video元素
                const video = document.createElement('video');
                video.className = 'demo-video';
                video.id = `player${i + 1}`;
                video.setAttribute('controls', 'controls');
                video.setAttribute('muted', '');

                // 创建loading元素
                const loading = document.createElement('div');
                loading.id = `loading${i + 1}`;
                loading.className = 'loading';
                loading.style.display = 'block';
                loading.textContent = '加载中...';

                // 组装结构
                videoBox.appendChild(video);
                videoBox.appendChild(loading);
                videoContainer.appendChild(videoHeader);
                videoContainer.appendChild(videoBox);

                // 将video-container包装在grid-item中以保持原有布局
                const item = document.createElement('div');
                item.className = 'grid-item';
                item.appendChild(videoContainer);

                container.appendChild(item);
            }

            window.addEventListener('resize', function() {
                const videos = document.querySelectorAll('.demo-video');
                videos.forEach(video => {
                    // 强制重新计算尺寸
                    video.style.width = '100%';
                    video.style.height = '100%';
                });
            });
        }
        
        // 初始化FLV视频播放器
        function initVideoPlayers() {
            const carTag = document.getElementById('paizhao');
            carTag.textContent = "车牌号：" + carID;

            if (typeof flvjs !== 'undefined') {
                // 初始化六个通道的播放器
                for (let i = 1; i <= 6; i++) {
                    videoPlayers[i] = new VideoPlayer(i);
                }
                console.log('六通道视频播放器初始化完成');
            } else {
                // 如果 flv.js 还没加载，等待 100ms 后重试
                setTimeout(initVideoPlayers, 100);
            }
        }

        // 加载视频（保留原有的图片加载逻辑作为备用）
        function initVideos() {
            const carTag = document.getElementById('paizhao');
            carTag.textContent = "车牌号：" + carID;

            const videoStreams = [
                "/808FrontProject/image/default.png",
                "/808FrontProject/image/default.png",
                "/808FrontProject/image/default.png",
                "/808FrontProject/image/default.png",
                "/808FrontProject/image/default.png",
                "/808FrontProject/image/default.png"
            ];

            const gridItems = document.querySelectorAll('.grid-item');
            gridItems.forEach((item, index) => {
                // 检查是否已有图片，避免重复插入
                if (!item.querySelector('.video-stream')) {
                    const video = document.createElement('img');
                    video.className = 'video-stream';
                    video.src = videoStreams[index];
                    video.style.width = '100%'; // 确保初始化时设置
                    video.style.height = '100%'; // 确保初始化时设置
                    item.insertBefore(video, item.firstChild); // 插入到信息框之前
                }
            });
        }
        
        
        
        function dateFormat(t){
        	const date = new Date(t);
        	const formattedDateTime = date.toLocaleString('zh-CN', {
        	  year: 'numeric',
        	  month: '2-digit',
        	  day: '2-digit',
        	  hour: '2-digit',
        	  minute: '2-digit',
        	  second: '2-digit',
        	  hour12: false
        	}).replace(/\//g, '-');
        	return formattedDateTime; //2023-04-15 14:30:45
        }
        
        async function checkRemoteFileExists(url) { //判断远程文件是否存在
        	  try {
        	    const response = await fetch(url, { method: 'HEAD' });
        	    return response.ok; // 200-299 状态码返回 true
        	  } catch (error) {
        	    console.error("检查远程文件出错:", error);
        	    return false;
        	  }
        	}

        //检查视频
        function checkVideos() {
        	var t=new Date().getTime();
        	const videoStreamsDefault = [
                "/808FrontProject/image/default.png",
                "/808FrontProject/image/default.png",
                "/808FrontProject/image/default.png",
                "/808FrontProject/image/default.png",
                "/808FrontProject/image/default.png",
                "/808FrontProject/image/default.png"
            ];
        	const serverip="*************";
        	//const serverip="localhost";
            const videoStreams = [
            	//"http://localhost:8001/video_feed/"+carID+"/2?t=" + t,
            	"http://"+serverip+":8001/video_feed/"+carID,
            	"http://"+serverip+":8001/video_feed/"+carID,
            	"http://"+serverip+":8001/video_feed/"+carID,
            	"http://"+serverip+":8001/video_feed/"+carID,
            	"http://"+serverip+":8001/video_feed/"+carID,
            	"http://"+serverip+":8001/video_feed/"+carID,
            	//"http://localhost:8001/video_feed/"+carID,
            	//"http://localhost:8001/video_feed/"+carID,
            	//"http://localhost:8001/video_feed/"+carID
            ];
          
            console.log("检查视频");
            //获取视频容器
            var container = document.getElementById('gridContainer');
            // 获取所有视频格子DIV对象
            var videoItems = container.querySelectorAll('.video-stream');
            var infoLefts=container.querySelectorAll('.info-left');
            // 遍历所有视频
            //if(iterindex==0){
            videoItems.forEach((video, index) => {
            	video.src=videoStreamsDefault[index];
            });
            //}
            //iterindex++;
            
            videoItems.forEach((video, index) => {
            	let c=initialIndex+index+1; //通道
                let videoUrl = videoStreams[index]+"/"+c+"?t="+t;
                //let videoUrl = videoStreams[index]+"/"+c;
                //if (video.width === 0) {
                    //console.log(`通道${index+1}: 视频流中断，尝试重新连接..`);
                    //videoUrl = 'http://localhost:8001/video_feed?t=' + t;
                //}
                infoLefts[index].textContent=dateFormat(t);
              /// if (video.width === 0|video.src==videoStreamsDefault[index]) {
                	video.src=videoUrl;
                //}
                //延时0.5s
                let date = Date.now();let currentDate = null;
                do {
                	  currentDate = Date.now();} 
                while (currentDate - date < 500);
                /*
                checkRemoteFileExists(videoUrl) .then(exists => {
                	if(exists==false){
                    	video.src=videoStreamsDefault[index];
                    	infoLefts[index].textContent='数据请求中...';
                    	console.log(`通道${index+1}: 视频流中断，尝试重新连接..`);
                    }else{
                    	infoLefts[index].textContent=dateFormat(t);
                    	video.src=videoUrl;
                    }
                });
                */
               
            });
             
        }
        
        function checkVideos_1() {
            var t = new Date().getTime();
            const videoStreamsDefault = [
                "/808FrontProject/image/default.png",
                "/808FrontProject/image/default.png",
                "/808FrontProject/image/default.png",
                "/808FrontProject/image/default.png",
                "/808FrontProject/image/default.png",
                "/808FrontProject/image/default.png"
            ];
            const serverip = "*************";
            const videoStreams = [
                "http://"+serverip+":8001/video_feed/"+carID,
                "http://"+serverip+":8001/video_feed/"+carID,
                "http://"+serverip+":8001/video_feed/"+carID,
                "http://"+serverip+":8001/video_feed/"+carID,
                "http://"+serverip+":8001/video_feed/"+carID,
                "http://"+serverip+":8001/video_feed/"+carID,
            ];

            console.log("检查视频");
            var container = document.getElementById('gridContainer');
            var videoItems = container.querySelectorAll('.video-stream');
            var infoLefts = container.querySelectorAll('.info-left');
            
            // 1. 先全部设置为默认图片
            videoItems.forEach((video, index) => {
            	if(index==0){
            		  video.src = videoStreamsDefault[index];
                      infoLefts[index].textContent = '加载中...';
            	}
              
            });
            
            // 2. 逐个尝试加载真实视频流
            videoItems.forEach((video, index) => {
            	if(index==0){
                let c = initialIndex + index + 1; //通道
                let videoUrl = videoStreams[index] + "/" + c + "?t=" + t;
                
                // 创建临时image对象测试连接
                let tester = new Image();
                tester.onload = function() {
                    // 加载成功才替换真实视频流
                    video.src = videoUrl;
                    infoLefts[index].textContent = dateFormat(t);
                };
                tester.onerror = function() {
                    // 保持默认图片
                    infoLefts[index].textContent = '连接失败';
                    console.log(`通道${index+1}: 视频流连接失败`);
                };
                tester.src = videoUrl;
            	}
            });
        }
        
        function checkVideos_2() {
            var t = new Date().getTime();
            const videoStreamsDefault = [
                "/808FrontProject/image/default.png",
                "/808FrontProject/image/default.png",
                "/808FrontProject/image/default.png",
                "/808FrontProject/image/default.png",
                "/808FrontProject/image/default.png",
                "/808FrontProject/image/default.png"
            ];
            const serverip = "*************";
            const serverip2="*************"
            const videoStreams = [
                "http://"+serverip+":8001/video_feed/"+carID,
                "http://"+serverip+":8001/video_feed/"+carID,
                "http://"+serverip+":8001/video_feed/"+carID,
                "http://"+serverip2+":8001/video_feed/"+carID,
                "http://"+serverip2+":8001/video_feed/"+carID,
                "http://"+serverip2+":8001/video_feed/"+carID,
            ];

            console.log("检查视频");
            var container = document.getElementById('gridContainer');
            var videoItems = container.querySelectorAll('.video-stream');
            var infoLefts = container.querySelectorAll('.info-left');
            
            // 1. 先全部设置为默认图片
            videoItems.forEach((video, index) => {
            	if(index==1){
                video.src = videoStreamsDefault[index];
                infoLefts[index].textContent = '加载中...';
            	}
            });
            
            // 2. 逐个尝试加载真实视频流
            videoItems.forEach((video, index) => {
            	
            	if(index==1){
                let c = initialIndex + index + 1; //通道
                let videoUrl = videoStreams[index] + "/" + c + "?t=" + t;
                
                // 创建临时image对象测试连接
                let tester = new Image();
                tester.onload = function() {
                    // 加载成功才替换真实视频流
                    video.src = videoUrl;
                    infoLefts[index].textContent = dateFormat(t);
                };
                tester.onerror = function() {
                    // 保持默认图片
                    infoLefts[index].textContent = '连接失败';
                    console.log(`通道${index+1}: 视频流连接失败`);
                };
                tester.src = videoUrl;
            	}
            });
        }
        
        function checkVideos_3() {
            var t = new Date().getTime();
            const videoStreamsDefault = [
                "/808FrontProject/image/default.png",
                "/808FrontProject/image/default.png",
                "/808FrontProject/image/default.png",
                "/808FrontProject/image/default.png",
                "/808FrontProject/image/default.png",
                "/808FrontProject/image/default.png"
            ];
            const serverip = "*************";
            const videoStreams = [
                "http://"+serverip+":8001/video_feed/"+carID,
                "http://"+serverip+":8001/video_feed/"+carID,
                "http://"+serverip+":8001/video_feed/"+carID,
                "http://"+serverip+":8001/video_feed/"+carID,
                "http://"+serverip+":8001/video_feed/"+carID,
                "http://"+serverip+":8001/video_feed/"+carID,
            ];

            console.log("检查视频");
            var container = document.getElementById('gridContainer');
            var videoItems = container.querySelectorAll('.video-stream');
            var infoLefts = container.querySelectorAll('.info-left');
            
            var currentIndex=2;
            
            // 1. 先全部设置为默认图片
            videoItems.forEach((video, index) => {
            	if(index==currentIndex){
                video.src = videoStreamsDefault[index];
                infoLefts[index].textContent = '加载中...';
            	}
            });
            
            // 2. 逐个尝试加载真实视频流
            videoItems.forEach((video, index) => {
            	
            	if(index==currentIndex){
                let c = initialIndex + index + 1; //通道
                let videoUrl = videoStreams[index] + "/" + c + "?t=" + t;
                
                // 创建临时image对象测试连接
                let tester = new Image();
                tester.onload = function() {
                    // 加载成功才替换真实视频流
                    video.src = videoUrl;
                    infoLefts[index].textContent = dateFormat(t);
                };
                tester.onerror = function() {
                    // 保持默认图片
                    infoLefts[index].textContent = '连接失败';
                    console.log(`通道${index+1}: 视频流连接失败`);
                };
                tester.src = videoUrl;
            	}
            });
        }
        
        function checkVideos_4() {
            var t = new Date().getTime();
            const videoStreamsDefault = [
                "/808FrontProject/image/default.png",
                "/808FrontProject/image/default.png",
                "/808FrontProject/image/default.png",
                "/808FrontProject/image/default.png",
                "/808FrontProject/image/default.png",
                "/808FrontProject/image/default.png"
            ];
            const serverip = "*************";
            const videoStreams = [
                "http://"+serverip+":8001/video_feed/"+carID,
                "http://"+serverip+":8001/video_feed/"+carID,
                "http://"+serverip+":8001/video_feed/"+carID,
                "http://"+serverip+":8001/video_feed/"+carID,
                "http://"+serverip+":8001/video_feed/"+carID,
                "http://"+serverip+":8001/video_feed/"+carID,
            ];

            console.log("检查视频");
            var container = document.getElementById('gridContainer');
            var videoItems = container.querySelectorAll('.video-stream');
            var infoLefts = container.querySelectorAll('.info-left');
            
            var currentIndex=3;
            
            // 1. 先全部设置为默认图片
            videoItems.forEach((video, index) => {
            	if(index==currentIndex){
                video.src = videoStreamsDefault[index];
                infoLefts[index].textContent = '加载中...';
            	}
            });
            
            // 2. 逐个尝试加载真实视频流
            videoItems.forEach((video, index) => {
            	
            	if(index==currentIndex){
                let c = initialIndex + index + 1; //通道
                let videoUrl = videoStreams[index] + "/" + c + "?t=" + t;
                
                // 创建临时image对象测试连接
                let tester = new Image();
                tester.onload = function() {
                    // 加载成功才替换真实视频流
                    video.src = videoUrl;
                    infoLefts[index].textContent = dateFormat(t);
                };
                tester.onerror = function() {
                    // 保持默认图片
                    infoLefts[index].textContent = '连接失败';
                    console.log(`通道${index+1}: 视频流连接失败`);
                };
                tester.src = videoUrl;
            	}
            });
        }
        
        function checkVideos_5() {
            var t = new Date().getTime();
            const videoStreamsDefault = [
                "/808FrontProject/image/default.png",
                "/808FrontProject/image/default.png",
                "/808FrontProject/image/default.png",
                "/808FrontProject/image/default.png",
                "/808FrontProject/image/default.png",
                "/808FrontProject/image/default.png"
            ];
            const serverip = "*************";
            const videoStreams = [
                "http://"+serverip+":8001/video_feed/"+carID,
                "http://"+serverip+":8001/video_feed/"+carID,
                "http://"+serverip+":8001/video_feed/"+carID,
                "http://"+serverip+":8001/video_feed/"+carID,
                "http://"+serverip+":8001/video_feed/"+carID,
                "http://"+serverip+":8001/video_feed/"+carID,
            ];

            console.log("检查视频");
            var container = document.getElementById('gridContainer');
            var videoItems = container.querySelectorAll('.video-stream');
            var infoLefts = container.querySelectorAll('.info-left');
            
            var currentIndex=4;
            
            // 1. 先全部设置为默认图片
            videoItems.forEach((video, index) => {
            	if(index==currentIndex){
                video.src = videoStreamsDefault[index];
                infoLefts[index].textContent = '加载中...';
            	}
            });
            
            // 2. 逐个尝试加载真实视频流
            videoItems.forEach((video, index) => {
            	
            	if(index==currentIndex){
                let c = initialIndex + index + 1; //通道
                let videoUrl = videoStreams[index] + "/" + c + "?t=" + t;
                
                // 创建临时image对象测试连接
                let tester = new Image();
                tester.onload = function() {
                    // 加载成功才替换真实视频流
                    video.src = videoUrl;
                    infoLefts[index].textContent = dateFormat(t);
                };
                tester.onerror = function() {
                    // 保持默认图片
                    infoLefts[index].textContent = '连接失败';
                    console.log(`通道${index+1}: 视频流连接失败`);
                };
                tester.src = videoUrl;
            	}
            });
        }
        
        
        function checkVideos_6() {
            var t = new Date().getTime();
            const videoStreamsDefault = [
                "/808FrontProject/image/default.png",
                "/808FrontProject/image/default.png",
                "/808FrontProject/image/default.png",
                "/808FrontProject/image/default.png",
                "/808FrontProject/image/default.png",
                "/808FrontProject/image/default.png"
            ];
            const serverip = "*************";
            const videoStreams = [
                "http://"+serverip+":8001/video_feed/"+carID,
                "http://"+serverip+":8001/video_feed/"+carID,
                "http://"+serverip+":8001/video_feed/"+carID,
                "http://"+serverip+":8001/video_feed/"+carID,
                "http://"+serverip+":8001/video_feed/"+carID,
                "http://"+serverip+":8001/video_feed/"+carID,
            ];

            console.log("检查视频");
            var container = document.getElementById('gridContainer');
            var videoItems = container.querySelectorAll('.video-stream');
            var infoLefts = container.querySelectorAll('.info-left');
            
            var currentIndex=5;
            
            // 1. 先全部设置为默认图片
            videoItems.forEach((video, index) => {
            	if(index==currentIndex){
                video.src = videoStreamsDefault[index];
                infoLefts[index].textContent = '加载中...';
            	}
            });
            
            // 2. 逐个尝试加载真实视频流
            videoItems.forEach((video, index) => {
            	
            	if(index==currentIndex){
                let c = initialIndex + index + 1; //通道
                let videoUrl = videoStreams[index] + "/" + c + "?t=" + t;
                
                // 创建临时image对象测试连接
                let tester = new Image();
                tester.onload = function() {
                    // 加载成功才替换真实视频流
                    video.src = videoUrl;
                    infoLefts[index].textContent = dateFormat(t);
                };
                tester.onerror = function() {
                    // 保持默认图片
                    infoLefts[index].textContent = '连接失败';
                    console.log(`通道${index+1}: 视频流连接失败`);
                };
                tester.src = videoUrl;
            	}
            });
        }
        
        function checkVideos_DS() {
            var t = new Date().getTime();
            const videoStreamsDefault = [
                "/808FrontProject/image/default.png",
                "/808FrontProject/image/default.png",
                "/808FrontProject/image/default.png",
                "/808FrontProject/image/default.png",
                "/808FrontProject/image/default.png",
                "/808FrontProject/image/default.png"
            ];
            const serverip = "*************";
            const videoStreams = [
                "http://"+serverip+":8001/video_feed/"+carID,
                "http://"+serverip+":8001/video_feed/"+carID,
                "http://"+serverip+":8001/video_feed/"+carID,
                "http://"+serverip+":8001/video_feed/"+carID,
                "http://"+serverip+":8001/video_feed/"+carID,
                "http://"+serverip+":8001/video_feed/"+carID,
            ];

            console.log("检查视频");
            var container = document.getElementById('gridContainer');
            var videoItems = container.querySelectorAll('.video-stream');
            var infoLefts = container.querySelectorAll('.info-left');
            
            // 1. 先全部设置为默认图片
            videoItems.forEach((video, index) => {
                video.src = videoStreamsDefault[index];
                infoLefts[index].textContent = '加载中...';
            });
            
            // 2. 逐个尝试加载真实视频流
            videoItems.forEach((video, index) => {
                let c = initialIndex + index + 1; //通道
                let videoUrl = videoStreams[index] + "/" + c + "?t=" + t;
                
                // 创建临时image对象测试连接
                let tester = new Image();
                tester.onload = function() {
                    // 加载成功才替换真实视频流
                    video.src = videoUrl;
                    infoLefts[index].textContent = dateFormat(t);
                };
                tester.onerror = function() {
                    // 保持默认图片
                    infoLefts[index].textContent = '连接失败';
                    console.log(`通道${index+1}: 视频流连接失败`);
                };
                tester.src = videoUrl;
            });
        }

        // 修改后的截图函数
        function takeScreenshot() {
        	//alert("截图");
        	console.log("点击截图！");
            // 只截取视频区域
            const gridContainer = document.getElementById('gridContainer');
            

            html2canvas(gridContainer, {
                scale: 1, // 控制截图质量，1=普通，2=高清
                logging: false,
                useCORS: true, // 允许跨域图片
                allowTaint: true, // 允许污染图片
                foreignObjectRendering: true, // 启用 SVG 渲染
                ignoreElements: (element) => {
                    return element.tagName === 'IFRAME'; // 忽略嵌套的 iframe
                }
            }).then(canvas => {
                const link = document.createElement('a');
                link.download = '监控截图_' + new Date().toISOString().replace(/[:.]/g, '-') + '.png';
                link.href = canvas.toDataURL('image/png');
                link.click();
            }).catch(err => {
                console.error('截图失败:', err);
                alert('截图失败，请重试或检查控制台错误信息');
            });
            
        }
        


        function toggleFullscreen() {
        	/*
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen();
                document.getElementById('exitFullscreen').style.display = 'block';
            } else {
                document.exitFullscreen();
                document.getElementById('exitFullscreen').style.display = 'none';
            }
        	*/
        	const targetElement = window.frameElement || document.documentElement;
        	const gridContainer = document.getElementById('gridContainer');
            if (!document.fullscreenElement) {
                targetElement.requestFullscreen().then(() => {
                    if (!window.frameElement) { // 仅当不是 iframe 时才显示退出按钮
                        document.getElementById('exitFullscreen').style.display = 'block';
                    }
                    gridContainer.setAttribute("title","按ESC即可退出全屏模式");
                }).catch(err => {
                    console.error('全屏错误:', err);
                });
            } else {
                document.exitFullscreen();
                if (!window.frameElement) {
                    document.getElementById('exitFullscreen').style.display = 'none';
                }
                gridContainer.setAttribute("title","");
            }
        }

        // 重连所有通道
        function reconnectAllChannels() {
            console.log('重新连接所有通道...');
            Object.values(videoPlayers).forEach((player, index) => {
                if (player) {
                    // 延迟启动，避免同时发起太多连接
                    setTimeout(() => {
                        player.replay();
                    }, index * 500);
                }
            });
        }

        // 重连指定通道
        function reconnectChannel(channel) {
            console.log(`重新连接通道${channel}...`);
            if (videoPlayers[channel]) {
                videoPlayers[channel].replay();
            }
        }

        window.onload = function () {
            document.getElementById('exitFullscreen').addEventListener('click', toggleFullscreen);
            document.addEventListener('fullscreenchange', function () {
                document.getElementById('exitFullscreen').style.display = document.fullscreenElement ? 'block' : 'none';
            });

            createGrid(gridNumber1, gridNumber);

            // 初始化FLV播放器（如果flv.js可用）
            if (typeof flvjs !== 'undefined') {
                initVideoPlayers();
            } else {
                // 回退到原有的图片显示方式
                initVideos();
                checkVideos();
            }

            // 每5秒检查一次连接
            // videoCheck=setInterval(checkVideos, 10000); //检查视频

            // 绑定按钮事件
            /*
            document.getElementById('close').addEventListener('click', () => {
                if (confirm('确定关闭页面吗？')){
                    clearInterval(videoCheck); // 关闭检查视频定时器
                    window.close(); //关闭窗口
                }
            });
            */
            document.getElementById('camera1').addEventListener('click', function(){
                if (videoPlayers[1]) {
                    reconnectChannel(1);
                } else {
                    checkVideos_1();
                }
            });
            document.getElementById('camera2').addEventListener('click', function(){
                if (videoPlayers[2]) {
                    reconnectChannel(2);
                } else {
                    checkVideos_2();
                }
            });
            document.getElementById('camera3').addEventListener('click', function(){
                if (videoPlayers[3]) {
                    reconnectChannel(3);
                } else {
                    checkVideos_3();
                }
            });
            document.getElementById('camera4').addEventListener('click', function(){
                if (videoPlayers[4]) {
                    reconnectChannel(4);
                } else {
                    checkVideos_4();
                }
            });
            document.getElementById('camera5').addEventListener('click', function(){
                if (videoPlayers[5]) {
                    reconnectChannel(5);
                } else {
                    checkVideos_5();
                }
            });
            document.getElementById('camera6').addEventListener('click', function(){
                if (videoPlayers[6]) {
                    reconnectChannel(6);
                } else {
                    checkVideos_6();
                }
            });
            document.getElementById('fullScreen').addEventListener('click', toggleFullscreen);
            document.getElementById('setting').addEventListener('click', function(){
                initialIndex = (initialIndex + gridNumber) % 6;
                if (Object.keys(videoPlayers).length > 0) {
                    // 如果有FLV播放器，重连所有通道
                    reconnectAllChannels();
                } else {
                    // 否则使用原有的检查视频逻辑
                    checkVideos_DS();
                }
            });
            
        };
    </script>
</head>

<body>
    <div class="tag">
        <div class="tag-left">
            <div style="font-weight: bolder;">预览画面</div>
            <div id="paizhao" style="color:#FF0">车牌号：</div>
        </div>
         <!--  <div id="close"><a href="#" style="color:#FFF" title="点击关闭页面">关闭</a></div>  -->
    </div>
    <div class="grid-container" id="gridContainer"></div>
    <div class="controls">
        <div id="mode">
            <select title="点击选择视频模式">
                <option value="biaoqing">标清</option>
                <option value="gaoqing">高清</option>
            </select>
        </div>
        
        <div id="camera1"><a href="#" title="点击对视频截图">刷新1</a></div>
        <div id="camera2"><a href="#" title="点击对视频截图">刷新2</a></div>
        <div id="camera3"><a href="#" title="点击对视频截图">刷新3</a></div>
        <div id="camera4"><a href="#" title="点击对视频截图">刷新4</a></div>
        <div id="camera5"><a href="#" title="点击对视频截图">刷新5</a></div>
        <div id="camera6"><a href="#" title="点击对视频截图">刷新6</a></div>
        <div id="fullScreen"><a href="#" title="点击全屏显示">全屏</a></div>
    </div>
    <div class="exit-fullscreen" id="exitFullscreen"><a href="#" style="color:#FFF" title="点击退出全屏">退出全屏</a></div>
</body>

</html>