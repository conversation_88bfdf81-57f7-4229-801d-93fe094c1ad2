/**
 * 编码处理工具类
 * 用于处理前端中文显示乱码问题
 */
var EncodingUtils = {
    
    /**
     * 设置页面编码
     */
    setPageEncoding: function() {
        // 设置页面编码
        document.charset = "UTF-8";
        document.characterSet = "UTF-8";
        
        // 添加meta标签确保编码
        var metaCharset = document.querySelector('meta[charset]');
        if (!metaCharset) {
            metaCharset = document.createElement('meta');
            metaCharset.setAttribute('charset', 'UTF-8');
            document.head.insertBefore(metaCharset, document.head.firstChild);
        }
        
        var metaContentType = document.querySelector('meta[http-equiv="Content-Type"]');
        if (!metaContentType) {
            metaContentType = document.createElement('meta');
            metaContentType.setAttribute('http-equiv', 'Content-Type');
            metaContentType.setAttribute('content', 'text/html; charset=UTF-8');
            document.head.appendChild(metaContentType);
        }
    },
    
    /**
     * 处理AJAX请求编码
     */
    setupAjaxEncoding: function() {
        // jQuery AJAX全局设置
        if (typeof $ !== 'undefined') {
            $.ajaxSetup({
                contentType: "application/x-www-form-urlencoded; charset=UTF-8",
                beforeSend: function(xhr) {
                    xhr.setRequestHeader("Accept-Charset", "UTF-8");
                }
            });
        }
    },
    
    /**
     * 解码可能的乱码字符串
     */
    decodeString: function(str) {
        if (!str) return str;
        
        try {
            // 尝试解码URL编码
            if (str.indexOf('%') !== -1) {
                return decodeURIComponent(str);
            }
            
            // 尝试解码HTML实体
            var textarea = document.createElement('textarea');
            textarea.innerHTML = str;
            return textarea.value;
        } catch (e) {
            console.warn('解码字符串失败:', e);
            return str;
        }
    },
    
    /**
     * 编码字符串用于传输
     */
    encodeString: function(str) {
        if (!str) return str;
        
        try {
            return encodeURIComponent(str);
        } catch (e) {
            console.warn('编码字符串失败:', e);
            return str;
        }
    },
    
    /**
     * 初始化编码设置
     */
    init: function() {
        this.setPageEncoding();
        this.setupAjaxEncoding();
        
        // 页面加载完成后再次确认编码设置
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', function() {
                EncodingUtils.setPageEncoding();
            });
        }
    }
};

// 自动初始化
EncodingUtils.init();
