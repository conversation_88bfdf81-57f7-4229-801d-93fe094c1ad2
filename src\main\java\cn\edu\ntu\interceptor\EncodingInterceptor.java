package cn.edu.ntu.interceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.struts2.ServletActionContext;

import com.opensymphony.xwork2.ActionInvocation;
import com.opensymphony.xwork2.interceptor.AbstractInterceptor;

/**
 * 编码拦截器 - 确保请求和响应都使用UTF-8编码
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class EncodingInterceptor extends AbstractInterceptor {
    
    private static final long serialVersionUID = 1L;
    
    @Override
    public String intercept(ActionInvocation invocation) throws Exception {
        
        HttpServletRequest request = ServletActionContext.getRequest();
        HttpServletResponse response = ServletActionContext.getResponse();
        
        // 设置请求编码
        if (request.getCharacterEncoding() == null) {
            request.setCharacterEncoding("UTF-8");
        }
        
        // 设置响应编码
        response.setCharacterEncoding("UTF-8");
        
        // 设置响应内容类型
        String contentType = response.getContentType();
        if (contentType == null || !contentType.contains("charset")) {
            if (contentType == null) {
                response.setContentType("text/html; charset=UTF-8");
            } else if (contentType.contains("application/json")) {
                response.setContentType("application/json; charset=UTF-8");
            } else if (contentType.contains("text/")) {
                response.setContentType(contentType + "; charset=UTF-8");
            }
        }
        
        // 设置响应头
        response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
        response.setHeader("Pragma", "no-cache");
        response.setHeader("Expires", "0");
        
        return invocation.invoke();
    }
}
