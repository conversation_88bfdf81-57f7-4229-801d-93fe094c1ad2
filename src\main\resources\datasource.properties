# MySQL 8.x 驱动配置
driverClassName=com.mysql.cj.jdbc.Driver

# 数据库连接配置 - 本地数据库（推荐用于开发环境）
#url=************************************************************************************************************************************************************************

# 远程数据库配置（如果本地数据库不可用，取消下面的注释并注释上面的url）
url=****************************************************************************************************************************************************************************

# 数据库认证信息
username=dbadmin
password=dbadmin

# 连接池配置
maxActive=300
maxIdle=50
maxWait=10000

# 连接验证配置
validationQuery=SELECT 1
testOnBorrow=true
testOnReturn=false
testWhileIdle=true
timeBetweenEvictionRunsMillis=30000
minEvictableIdleTimeMillis=60000