package cn.edu.ntu.utils.db;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.Properties;
import java.io.InputStream;

/**
 * 数据库连接测试工具
 * 用于测试数据库连接是否正常
 * 
 * <AUTHOR>
 * @date 2024-07-04
 */
public class DatabaseConnectionTest {
    
    public static void main(String[] args) {
        testDatabaseConnection();
    }
    
    /**
     * 测试数据库连接
     */
    public static void testDatabaseConnection() {
        Properties props = new Properties();
        InputStream is = null;
        Connection conn = null;
        
        try {
            // 加载数据库配置
            is = DatabaseConnectionTest.class.getClassLoader()
                    .getResourceAsStream("datasource.properties");
            
            if (is == null) {
                System.err.println("❌ 无法找到 datasource.properties 配置文件");
                return;
            }
            
            props.load(is);
            
            String driverClassName = props.getProperty("driverClassName");
            String url = props.getProperty("url");
            String username = props.getProperty("username");
            String password = props.getProperty("password");
            
            System.out.println("=== 数据库连接测试 ===");
            System.out.println("驱动类: " + driverClassName);
            System.out.println("连接URL: " + url);
            System.out.println("用户名: " + username);
            System.out.println("密码: " + (password != null ? "***" : "null"));
            System.out.println();
            
            // 加载驱动
            System.out.print("正在加载MySQL驱动... ");
            Class.forName(driverClassName);
            System.out.println("✅ 成功");
            
            // 测试连接
            System.out.print("正在连接数据库... ");
            conn = DriverManager.getConnection(url, username, password);
            System.out.println("✅ 连接成功！");
            
            // 测试查询
            System.out.print("正在测试数据库查询... ");
            java.sql.Statement stmt = conn.createStatement();
            java.sql.ResultSet rs = stmt.executeQuery("SELECT 1 as test");
            if (rs.next()) {
                System.out.println("✅ 查询测试成功！");
            }
            rs.close();
            stmt.close();
            
            System.out.println("\n🎉 数据库连接测试完全成功！");
            
        } catch (ClassNotFoundException e) {
            System.err.println("❌ MySQL驱动未找到: " + e.getMessage());
            System.err.println("请检查pom.xml中是否包含MySQL驱动依赖");
        } catch (SQLException e) {
            System.err.println("❌ 数据库连接失败: " + e.getMessage());
            System.err.println("\n可能的解决方案:");
            System.err.println("1. 检查数据库服务是否启动");
            System.err.println("2. 检查网络连接");
            System.err.println("3. 验证用户名和密码");
            System.err.println("4. 检查数据库URL是否正确");
            System.err.println("5. 检查防火墙设置");
            
            // 提供具体的错误代码分析
            int errorCode = e.getErrorCode();
            String sqlState = e.getSQLState();
            System.err.println("\nSQL错误代码: " + errorCode);
            System.err.println("SQL状态: " + sqlState);
            
            if (e.getMessage().contains("Connection timed out")) {
                System.err.println("\n⚠️  连接超时 - 建议切换到本地数据库配置");
            } else if (e.getMessage().contains("Access denied")) {
                System.err.println("\n⚠️  访问被拒绝 - 请检查用户名和密码");
            } else if (e.getMessage().contains("Unknown database")) {
                System.err.println("\n⚠️  数据库不存在 - 请创建nettyserver数据库");
            }
            
        } catch (Exception e) {
            System.err.println("❌ 测试过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        } finally {
            // 关闭资源
            try {
                if (conn != null && !conn.isClosed()) {
                    conn.close();
                    System.out.println("数据库连接已关闭");
                }
                if (is != null) {
                    is.close();
                }
            } catch (Exception e) {
                System.err.println("关闭资源时发生错误: " + e.getMessage());
            }
        }
    }
    
    /**
     * 测试指定的数据库连接参数
     */
    public static boolean testConnection(String url, String username, String password) {
        try {
            Class.forName("com.mysql.cj.jdbc.Driver");
            Connection conn = DriverManager.getConnection(url, username, password);
            conn.close();
            return true;
        } catch (Exception e) {
            System.err.println("连接测试失败: " + e.getMessage());
            return false;
        }
    }
}
