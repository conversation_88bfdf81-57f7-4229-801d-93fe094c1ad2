C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\action\DriverAction.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\common\implement\VehicleInfoDaoImpl.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\service\common\implement\DriverInfoServiceImpl.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\service\report\implement\IgnitionReportServiceImpl.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\utils\db\JDBCUtilForReport.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\action\LoginAction.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\utils\others\ConstantUtils.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\common\implement\EndParamsDaoImpl.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\common\interfaces\UserInfoDao.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\report\implement\DriveReportDaoImpl.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\entity\common\VehicleGpsInfoForHistory.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\quartz\WarningReportJob.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\mapper\implement\DriverReportMapper.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\common\implement\MultiMediaEventDaoImpl.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\mapper\implement\UserInfoMapper.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\entity\common\RoundAreaSetting.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\mapper\implement\RectangleAreaSettingMapper.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\mapper\implement\DriverInfoMapper.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\entity\common\BaiduOffsetInfo.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\service\common\implement\ShotSettingRedisServiceImpl.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\service\common\interfaces\MultiMediaEventService.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\service\common\implement\VehiclePhotoRedisService.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\action\EndParamsAction.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\utils\others\CheckCodeImageUtil.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\action\LocationAction.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\entity\common\VehicleStatusInfo.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\common\implement\EFenceDaoImpl.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\entity\common\QueryVehicleInfo.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\service\common\interfaces\EFenceService.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\common\interfaces\DriverInfoDao.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\entity\report\IgnitionReport.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\common\implement\UploadFileDaoImpl.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\mapper\implement\DriveReportMapper.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\common\implement\GoogleOffsetInfoDaoImpl.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\quartz\DriveReportJob.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\service\common\interfaces\PositionReportRedisService.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\common\implement\QueryMessageDaoImpl.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\mapper\implement\ImageByteMapper.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\service\common\interfaces\UploadFileService.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\common\implement\BaiduOffsetInfoDaoImpl.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\service\report\implement\VelocityReportServiceImpl.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\action\VehicleGroupTreeAction.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\service\common\interfaces\ShotSettingRedisService.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\entity\common\VehicleGroupInfo.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\mapper\implement\LicenseParamsMapper.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\mapper\implement\ParkReportMapper.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\mapper\implement\WarningReportMapper.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\service\common\interfaces\VehicleInfoAndMemberInfoService.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\utils\db\JDBCTemplateForReport.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\service\common\implement\MemberInfoServiceImpl.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\service\common\implement\TrackInfoServiceImpl.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\common\interfaces\VehicleTrackDao.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\service\common\implement\EndParamsServiceImpl.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\entity\common\RectangleAreaSettingParam.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\service\common\interfaces\EndParamsService.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\report\implement\MileageReportDaoImpl.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\service\common\implement\UserInfoServiceImpl.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\service\common\interfaces\VehicleGroupInfoService.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\quartz\TrackRelateReportJob.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\mapper\implement\FatigueDrivingParamsMapper.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\action\ReportAction.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\mapper\implement\ServerParamsMapper.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\entity\common\ShotCommandParams.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\service\common\implement\CommonServiceImpl.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\common\interfaces\UserRelateGroupInfoDao.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\entity\report\TrackRelateReport.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\action\CommandInfoAction.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\common\implement\DriverInfoDaoImpl.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\report\interfaces\DriveReportDao.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\entity\common\PosParams.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\service\report\interfaces\VelocityReportService.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\common\implement\EndContolDaoImpl.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\mapper\implement\StringMapper.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\entity\common\SearchTrackResult.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\service\common\interfaces\EndControlService.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\service\report\interfaces\DriveReportService.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\service\report\interfaces\TrackRelateReportService.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\mapper\interfaces\ObjectMapper.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\service\common\implement\MessageQueryServiceImpl.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\service\common\interfaces\VehicleGpsInfoService.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\entity\common\MultiMediaEventInformation.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\common\implement\VehicleStatusInfoDaoImpl.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\common\implement\VehicleGroupTreeDaoImpl.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\mapper\implement\VehicleInfoMapper.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\mapper\implement\IcCardAuthenticationParamsMapper.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\common\interfaces\UploadFileDao.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\common\interfaces\ImageDao.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\service\common\interfaces\DriverInfoService.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\service\common\implement\UploadFileServiceImpl.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\entity\common\UploadedDriverInfo.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\service\common\implement\ImageServiceImpl.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\common\implement\VehicleTrackDaoImpl.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\report\interfaces\MileageReportDao.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\common\interfaces\CommandInfoDao.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\mapper\implement\VehicleInfoInGroupMapper2.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\utils\others\Integer1.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\report\interfaces\DriverReportDao.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\action\ValidationAction.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\mapper\implement\VehicleInfoMapper2.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\common\interfaces\LocationDao.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\service\report\interfaces\WarningReportService.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\entity\common\OverSpeedParams.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\entity\common\TerminalParams.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\action\UploadFileAction.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\mapper\implement\VehicleGpsInfoMapper.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\entity\common\ImageInfo.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\common\interfaces\VehicleInfoDao.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\common\implement\AreoSettingDaoImpl.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\common\implement\MemberInfoDaoImpl.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\entity\report\TrackRelateReportList.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\mapper\implement\RoundAreaSettingMapper.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\service\common\implement\CommandInfoServiceImpl.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\common\interfaces\VehicleStatusInfoDao.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\service\common\implement\EndControlServiceImpl.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\entity\common\AreaSetting.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\entity\common\VehicleGpsInfo.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\mapper\implement\VehicleInfoInGroupMapper.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\entity\common\CommandInfo.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\service\common\interfaces\MemberInfoService.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\entity\common\VehicleInfo.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\report\implement\WarningReportDaoImpl.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\entity\common\ServerParams.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\entity\common\StoreShotSettingResult.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\service\common\implement\EFenceServiceImpl.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\mapper\implement\VehicleGpsInfoMapperForHistory.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\utils\db\TransactionTemplate.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\entity\common\UserInfo.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\service\common\implement\VehicleInfoServiceImpl.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\service\common\implement\VehicleInfoAndMemberInfoServiceImpl.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\common\implement\VehicleRelateGroupInfoDaoImpl.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\message\req\PositionReport.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\utils\db\JDBCUtilForGpsHistory.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\action\VehicleGpsAction.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\common\interfaces\MemberInfoDao.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\action\MessageQueryAction.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\service\common\interfaces\MultiMediaEventInfoRedisService.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\service\report\implement\ParkReportServiceImpl.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\entity\report\WarningReport.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\service\common\implement\MultiMediaEventServiceImpl.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\service\common\interfaces\CommandInfoService.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\common\interfaces\MultiMediaEventDao.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\utils\db\JDBCTemplate.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\quartz\ParkReportJob.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\service\common\interfaces\VehicleInfoService.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\service\report\interfaces\IgnitionReportService.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\entity\common\RectangleAreaSetting.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\mapper\implement\IgnitionReportMapper.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\service\report\implement\MileageReportServiceImpl.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\service\common\implement\MultiMediaEventInfoRedisServiceImpl.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\common\interfaces\VehicleGroupTreeDao.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\action\MultiMediaEventAction.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\net\mapeye\core\CommonTools.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\mapper\implement\QueryMessageMapper.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\entity\common\PhotoDescription.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\entity\common\TableInfo.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\service\common\interfaces\VehicleGroupTreeService.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\utils\others\GlobalVariables.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\mapper\implement\CommandInfoMapper.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\service\report\interfaces\DriverReportService.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\mapper\implement\BaiduOffsetInfoMapper.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\entity\common\PolygonVertexSetting.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\service\report\interfaces\MileageReportService.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\utils\others\MultiMediaEventInfo.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\mapper\implement\MultiMediaEventInfoMapper.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\entity\common\QueryMessage.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\common\interfaces\OffsetInfoDao.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\service\common\implement\VehicleGpsInfoServiceImpl.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\mapper\implement\OverSpeedParamsMapper.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\entity\common\FatigueDrivingParams.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\common\interfaces\AreaSettingDao.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\common\interfaces\VehicleRelateGroupInfoDao.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\service\report\implement\DriverReportServiceImpl.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\entity\report\DriverReport.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\service\common\implement\LocationServiceImpl.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\quartz\IgnitionReportJob.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\service\common\interfaces\CommonService.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\action\UserAction.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\entity\common\MemberInfo.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\quartz\VelocityReportJob.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\entity\report\DriveReport.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\action\EFenceAction.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\service\common\interfaces\VehiclePhotoRedisService.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\entity\common\LicenseParams.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\utils\others\MultiMediaEventInfoRedisService.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\report\interfaces\VelocityReportDao.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\mapper\implement\GoogleOffsetInfoMapper.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\utils\db\MySQL5Dailect.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\entity\common\VehicleTotalInfo.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\entity\common\VehicleGroupStatusInfo.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\service\common\implement\PositionReportAssistRedisServiceImpl.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\action\VehicleAction.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\service\common\interfaces\TrackInfoService.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\common\implement\ImageDaoImpl.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\mapper\implement\PolygonVertexSettingMapper.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\action\PhotoAction.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\mapper\implement\ImageInfoMapper.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\common\interfaces\VehicleGroupInfoDao.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\common\interfaces\EndControlDao.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\message\req\RequestMessage.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\service\report\implement\DriveReportServiceImpl.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\report\implement\IgnitionReportDaoImpl.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\report\ReportJob.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\common\implement\UserRelateGroupInfoDaoImpl.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\mapper\implement\VehicleGroupInfoMapper.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\utils\db\TransactionManager.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\common\implement\PolygonAreaSettingMapper.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\service\common\implement\VehicleGroupInfoServiceImpl.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\common\interfaces\VehicleGpsInfoDao.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\service\report\implement\WarningReportServiceImpl.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\common\interfaces\EFenceDao.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\report\implement\DriverReportDaoImpl.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\report\implement\ParkReportDaoImpl.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\utils\others\CommunicateWithPlatformUtil.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\utils\others\CheckCodeUtil.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\entity\report\MileageReport.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\entity\common\PolygonAreaSetting.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\utils\others\PositionReportAssist.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\report\AlterTableJob.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\utils\others\VehiclePhotoRedisService.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\common\implement\LocationDaoImpl.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\service\common\interfaces\PositionReportAssistRedisService.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\common\implement\UploadedDriverInfoMapper1.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\entity\common\ShotSetting.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\service\report\implement\DefaultReportService.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\entity\common\DriverInfo.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\entity\common\GoogleOffsetInfo.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\report\interfaces\WarningReportDao.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\action\VehicleGroupAction.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\entity\common\DynatreeNode.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\service\common\interfaces\ImageService.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\entity\common\VehicleWarningInfo.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\utils\others\ArrayUtils.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\service\common\interfaces\LocationService.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\service\common\interfaces\MessageQueryService.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\service\report\implement\TrackRelateReportServiceImpl.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\utils\others\VehiclePhoto.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\mapper\implement\UploadedDriverInfoMapper.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\report\interfaces\IgnitionReportDao.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\mapper\implement\IntegerMapper.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\service\common\implement\PositionReportRedisServiceImpl.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\report\interfaces\ParkReportDao.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\mapper\implement\MemberInfoMapper.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\report\CreateGpsData.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\service\common\implement\VehicleGroupTreeServiceImpl.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\action\EndControlAction.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\common\implement\UserInfoDaoImpl.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\report\implement\VelocityReportDaoImpl.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\entity\report\ParkReport.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\action\TrackAction.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\entity\common\PolygonAreaSettingParam.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\common\implement\VehicleGroupInfoDaoImpl.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\mapper\implement\VelocityReportMapper.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\service\common\interfaces\UserInfoService.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\common\implement\CommandInfoDaoImpl.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\mapper\implement\TerminalParamsMapper.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\entity\common\RoundAreaSettingParam.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\utils\db\JDBCTemplateForGpsHistory.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\common\interfaces\QueryMessageDao.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\mapper\implement\PosParamsMapper.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\entity\common\OffsetInfo.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\common\implement\VehicleGpsInfoDaoImpl.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\entity\report\VelocityReport.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\utils\db\DateParseUtil.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\entity\common\IcCardAuthenticationParams.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\dao\common\interfaces\EndParamsDao.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\service\report\interfaces\ParkReportService.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\entity\common\VehicleInfoInGroup.java
C:\Users\<USER>\eclipse-worksapce-2\808FrontProject\src\main\java\cn\edu\ntu\quartz\CreateTableJob.java
